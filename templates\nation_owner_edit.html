{% extends "layout.html" %}
{% from "macros/tech_tree.html" import render_tech_tree %}

{% block title %}Edit Nation: {{ nation.name }}{% endblock %}

{% block content %}
<div class="nation-page-container">
    <div class="header-bar">
        <h1>Edit Nation: {{ nation.name }}</h1>
        <a href="{{ request.path.replace('edit', 'item') }}" class="back-button">Back to View</a>
    </div>
    
    {% if g.user.is_admin %}
        {% set default_action = request.path ~ '/save' %}
    {% else %}
        {% set default_action = request.path ~ '/request' %}
    {% endif %}
    
    <form method="POST" action="{{ default_action }}">
        {{ form.csrf_token }}
        
        <div class="expandable-sections">
            <!-- General Information Section -->
            <div class="expandable-section" id="general-info-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>General Information</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="main-columns">
                        <div class="table-wrapper">
                            <table class="info-table">
                                <tr class="resources-header-row">
                                    <th colspan="2" class="resources-header">General</th>
                                </tr>
                                <tr>
                                    <th>{{ form.name.label.text }}:</th>
                                    <td>{{ form.name(class="form-control") }}</td>
                                </tr>
                                <tr>
                                    <th>{{ form.region.label.text }}:</th>
                                    <td>{{ form.region(class="form-control") }}</td>
                                </tr>
                                {% if nation.empire %}
                                    <tr>
                                        <th>{{ form.prestige.label.text }}:</th>
                                        <td>{{ form.prestige(class="form-control") }}</td>
                                    </tr>
                                {% endif %}
                                <tr>
                                    <th>{{ form.stability.label.text }}:</th>
                                    <td>{{ form.stability(class="form-control") }}</td>
                                </tr>
                                <tr>
                                    <th>{{ schema.properties.stability_gain_chance.label }}:</th>
                                    <td>{{ (nation.stability_gain_chance * 100)|int }}%</td>
                                </tr>
                                <tr>
                                    <th>{{ schema.properties.stability_loss_chance.label }}:</th>
                                    <td>{{ (nation.stability_loss_chance * 100)|int }}%</td>
                                </tr>
                                <tr>
                                    <th>{{ form.infamy.label.text }}:</th>
                                    <td>{{ form.infamy(class="form-control") }}</td>
                                </tr>
                                <tr>
                                    <th>{{ form.temporary_karma.label.text }}:</th>
                                    <td>{{ form.temporary_karma(class="form-control") }}</td>
                                </tr>
                                {% if nation.nomadic > 0 %}
                                    <tr>
                                        <th>{{ schema.properties.migration_distance.label }}:</th>
                                        <td>{{ nation.migration_distance }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ schema.properties.stationary_delay.label }}:</th>
                                        <td>{{ nation.stationary_delay }}</td>
                                    </tr>
                                {% endif %}
                                {% if nation.vampirism_chance > 0 %}
                                    <tr>
                                        <th>{{ schema.properties.vampirism_chance.label }}:</th>
                                        <td>{{ (nation.vampirism_chance * 100)|int }}%</td>
                                    </tr>
                                {% endif %}
                                {% if nation.pop_loss_chance > 0 %}
                                    <tr>
                                        <th>{{ schema.properties.pop_loss_chance.label }}:</th>
                                        <td>{{ (nation.pop_loss_chance * 100)|int }}%</td>
                                    </tr>
                                {% endif %}
                            </table>
                        </div>
                                                
                        <div class="table-wrapper">
                            <table class="info-table">
                                <tr class="resources-header-row">
                                    <th colspan="2" class="resources-header">Income</th>
                                </tr>
                                <tr>
                                    <th>{{ form.money.label.text }}:</th>
                                    <td>{{ form.money(class="form-control") }} / {{ nation.money_capacity }}</td>
                                </tr>
                                <tr>
                                    <th>{{ schema.properties.money_income.label }}:</th>
                                    <td>{{ nation.money_income }}</td>
                                </tr>
                                <tr>
                                    <th>{{ schema.properties.remaining_import_slots.label }}:</th>
                                    <td>{{ nation.remaining_import_slots }}</td>
                                </tr>
                                <tr>
                                    <th>{{ schema.properties.remaining_export_slots.label }}:</th>
                                    <td>{{ nation.remaining_export_slots }}</td>
                                </tr>
                                <tr>
                                    <th>{{ schema.properties.trade_distance.label }}:</th>
                                    <td>{{ nation.trade_distance }}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="table-wrapper">
                            <table class="info-table">
                                <tr class="resources-header-row">
                                    <th colspan="2" class="resources-header">Miscellaneous</th>
                                </tr>
                                <tr>
                                    <th>{{ schema.properties.origin.label }}:</th>
                                    <td>{{ form.origin(class="form-control") }}</td>
                                </tr>
                                {% if view_access_level >= schema.properties.temperament.view_access_level %}
                                    <tr>
                                        <th>{{ schema.properties.temperament.label }}:</th>
                                        <td>{{ form.temperament(class="form-control") }}</td>
                                    </tr>
                                {% else %}
                                    <input type="hidden" name="temperament" value="{{ nation.temperament }}">
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Administration & Holdings Section -->
            <div class="expandable-section" id="administration-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>Administration & Holdings</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="table-wrapper">
                        <table class="info-table">
                            <tr class="resources-header-row">
                                <th colspan="2" class="resources-header">Administration</th>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.administration.label }}:</th>
                                <td>{{ nation.administration }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.passive_expansion_chance.label }}:</th>
							    <td>{{ (nation.get("passive_expansion_chance", 0) * 100)|int }}%</td>
                            </tr>
                            <tr>
                                <th>Territory:</th>
                                <td>{{ nation.current_territory }} / {{ nation.effective_territory }}</td>
                            </tr>
                            <tr>
                                <th>Roads:</th>
                                <td>{{ form.road_usage(class="form-control") }} / {{ nation.road_capacity }}</td>
                            </tr>
                        </table>
                        <table class="info-table">
                            <tr class="resources-header-row">
                                <th colspan="2" class="resources-header">Territory Holdings</th>
                            </tr>
                            {% for terrain, details in json_data["terrains"].items() %}
                                <tr>
                                    <th>{{ details["display_name"] }}</th>
                                    <td>{{ form.territory_types[terrain](class="form-control") }}</td>
                                </tr>
                            {% endfor %}
                        </table>

                    </div>
                </div>
            </div>


            
            <!-- Demographics Section -->
            <div class="expandable-section" id="demographics-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>Demographics & Population</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="table-wrapper">
                        <table class="info-table">
                            <tr class="resources-header-row">
                                <th colspan="2" class="resources-header">Demographics</th>
                            </tr>
                            <tr>
                                <th>{{ form.primary_race.label.text }}:</th>
                                <td>{{ form.primary_race(class="form-control") }}</td>
                            </tr>
                            <tr>
                                <th>{{ form.primary_culture.label.text }}:</th>
                                <td>{{ form.primary_culture(class="form-control") }}</td>
                            </tr>
                            <tr>
                                <th>{{ form.primary_religion.label.text }}:</th>
                                <td>{{ form.primary_religion(class="form-control") }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.unique_minority_count.label }}:</th>
                                <td>{{ nation.unique_minority_count }}</td>
                            </tr>
                        </table>
                        
                        <table class="info-table">
                            <thead>
                                <tr class="resources-header-row">
                                    <th colspan="7" class="resources-header">Population Details</th>
                                </tr>
                                <tr>
                                    <th>#</th>
                                    <th>Race</th>
                                    <th>Culture</th>
                                    <th>Religion</th>
                                    <th>Reason</th>
                                    {% if g.user.is_admin %}
                                        <th>Clone Now</th>
                                    {% endif %}
                                    <th>Request Clone</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for pop in linked_objects["pops"] %}
                                    <tr>
                                        <td><a href="{{ pop["link"] }}">{{ loop.index }}</a></td>
                                        <td>
                                        {% if pop["linked_objects"]["race"] %}
                                            <a href="{{ pop['linked_objects']['race']['link'] }}">{{ pop['linked_objects']['race']['name'] }}</a>
                                        {% else %}
                                            None
                                        {% endif %}
                                        </td>
                                        <td>
                                        {% if pop["linked_objects"]["culture"] %}
                                            <a href="{{ pop['linked_objects']['culture']['link'] }}">{{ pop['linked_objects']['culture']['name'] }}</a>
                                        {% else %}
                                            None
                                        {% endif %}
                                        </td>
                                        <td>
                                        {% if pop["linked_objects"]["religion"] %}
                                            <a href="{{ pop['linked_objects']['religion']['link'] }}">{{ pop['linked_objects']['religion']['name'] }}</a>
                                        {% else %}
                                            None
                                        {% endif %}
                                        </td>
                                        <form method="POST" action="{{ pop['link'].replace('item', 'clone') }}/save" style="display: inline;">
                                            <td>
                                                <input type="text" id="pop_reason" name="pop_reason" value="">
                                            </td>
                                            
                                            {% if g.user.is_admin %}
                                                <td><button type="submit" formaction="{{ pop['link'].replace('item', 'clone') }}/save" class="btn btn-primary">Clone Now</button></td>
                                            {% endif %}
                                            <td><button type="submit" formaction="{{ pop['link'].replace('item', 'clone') }}/request" class="btn btn-primary">Request Clone</button></td>
                                        </form>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Government & Laws Section -->
            <div class="expandable-section" id="government-laws-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>Government & Laws</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="table-wrapper">
                        <table class="info-table">
                            <tr class="resources-header-row">
                                <th colspan="2" class="resources-header">Government & Laws</th>
                            </tr>
                            {% for law in schema.laws %}
                                {% if law != "stability" and law != "vassal_type" and law in form._fields.keys() %}
                                    <tr>
                                        <th>{{ form[law].label.text }}:</th>
                                        <td>{{ form[law](class="form-control") }}</td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Vassalship Section -->
            <div class="expandable-section" id="vassalship-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>Vassalship</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="table-wrapper">
                        <table class="info-table">
                            <tr class="resources-header-row">
                                <th colspan="2" class="resources-header">Vassalship</th>
                            </tr>
                            <tr>
                                <th>{{ form.overlord.label.text }}:</th>
                                <td>{{ form.overlord(class="form-control") }}</td>
                            </tr>
                            <tr>
                                <th>{{ form.vassal_type.label.text }}:</th>
                                <td>{{ form.vassal_type(class="form-control") }}</td>
                            </tr>
                            <tr>
                                <th>{{ form.compliance.label.text }}:</th>
                                <td>{{ form.compliance(class="form-control") }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.disobey_chance.label }}:</th>
                                <td>{{ (nation.disobey_chance * 100)|int }}%</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.rebellion_chance.label }}:</th>
                                <td>{{ (nation.rebellion_chance * 100)|int }}%</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.concessions_chance.label }}:</th>
                                <td>{{ (nation.concessions_chance * 100)|int }}%</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.concessions_qty.label }}:</th>
                                <td>{{ nation.concessions_qty }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.concessions.label }}:</th>
                                <td>
                                    <div id="concessions">
                                        {% if nation.get("concessions", None) is mapping %}
                                            {% for resource, amount in nation.get("concessions", {}).items() %}
                                                {{ resource }}: {{ amount }}<br>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                    {{ form.concessions(id="concessions-field") }}
                                    <button type="button" class="btn btn-primary" onclick="resetConcessions()">Reset Concessions</button>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Diplomacy Section -->
            <div class="expandable-section" id="diplomacy-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>Diplomacy</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="table-wrapper">
                        <table class="info-table">
                            <tr class="resources-header-row">
                                <th colspan="2" class="resources-header">General</th>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.military_alliance_slots.label }}:</th>
                                <td>{{ nation.military_alliance_slots }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.defensive_pact_slots.label }}:</th>
                                <td>{{ nation.defensive_pact_slots }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.non_aggression_pact_slots.label }}:</th>
                                <td>{{ nation.non_aggression_pact_slots }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="table-wrapper" id="diplomatic-relations-table">
                        <table class="info-table">
                            <thead>
                                <tr class="resources-header-row">
                                    <th colspan="5" class="resources-header">Diplomatic Relations</th>
                                </tr>
                                <tr>
                                    <th>Diplomatic Relation</th>
                                    <th>Nation</th>
                                    <th>Relation</th>
                                    <th>Pact Type</th>
                                    <th>Temperament</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for relation in linked_objects["diplomatic_relations_1"] %}
                                    <tr>
                                        <td><a href="{{ relation['link'] }}">{{ loop.index }}</a></td>
                                        <td>
                                        {% if relation["linked_objects"]["nation_2"] %}
                                            <a href="{{ relation['linked_objects']['nation_2']['link'] }}">{{ relation['linked_objects']['nation_2']['name'] }}</a>
                                        {% else %}
                                            None
                                        {% endif %}
                                        </td>
                                        <td>{{ relation["relation"] }}</td>
                                        <td>{{ relation["pact_type"] }}</td>
                                        {% if user_is_owner or view_access_level >= schema.properties.temperament.view_access_level %}
                                            <td>{{ relation["temperament"] }}</td>
                                        {% else %}
                                            <td>Hidden</td>
                                        {% endif %}
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="expandable-section" id="war-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>War</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="table-wrapper">
                        <h2>War Modifiers</h2>
                        <table class="info-table">
                            <tr>
                                <th>{{ schema.properties.land_attack.label }}:</th>
                                <td>{{ nation.land_attack }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.land_defense.label }}:</th>
                                <td>{{ nation.land_defense }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.naval_attack.label }}:</th>
                                <td>{{ nation.naval_attack }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.naval_defense.label }}:</th>
                                <td>{{ nation.naval_defense }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.mercenary_land_attack.label }}:</th>
                                <td>{{ nation.mercenary_land_attack }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.mercenary_land_defense.label }}:</th>
                                <td>{{ nation.mercenary_land_defense }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.mercenary_naval_attack.label }}:</th>
                                <td>{{ nation.mercenary_naval_attack }}</td>
                            </tr>
                            <tr>
                                <th>{{ schema.properties.mercenary_naval_defense.label }}:</th>
                                <td>{{ nation.mercenary_naval_defense }}</td>
                            </tr>
                        </table>
                        
                        <h2>Land Units</h2>
                        <p>Land Units: {{ nation.land_unit_count }}/{{ nation.land_unit_capacity }}</p>
                        <table class="jobs-table">
                            <thead>
                                <tr>
                                    <th>Unit</th>
                                    <th>Unit Count</th>
                                    <th>Upkeep</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for unit, details in nation.land_unit_details.items() %}
                                    <tr>
                                        <td>{{ details.display_name }}</td>
                                        <td>{{ form.land_units[unit] }}</td>
                                        <td>
                                        {% if details.upkeep is defined %}
                                            {% for resource, cost in details.upkeep.items() %}
                                                {{ resource }}: {{ cost }}<br>
                                            {% endfor %}
                                        {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <h2>Naval Units</h2>
                        <p>Naval Units: {{ nation.naval_unit_count }}/{{ nation.naval_unit_capacity }}</p>
                        <table class="jobs-table">
                            <thead>
                                <tr>
                                    <th>Unit</th>
                                    <th>Unit Count</th>
                                    <th>Upkeep</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for unit, details in nation.naval_unit_details.items() %}
                                    <tr>
                                        <td>{{ details.display_name }}</td>
                                        <td>{{ form.naval_units[unit] }}</td>
                                        <td>
                                        {% if details.upkeep is defined %}
                                            {% for resource, cost in details.upkeep.items() %}
                                                {{ resource }}: {{ cost }}<br>
                                            {% endfor %}
                                        {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Resources & Jobs Section -->
            <div class="expandable-section" id="resources-jobs-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h2>Resources & Jobs</h2>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <h2>Resources</h2>
                    <div class="table-wrapper">
                        <table class="resources-table">
                            <thead>
                                <tr class="resources-header-row">
                                    <th colspan="6" class="resources-header">General Resources</th>
                                </tr>
                                <tr>
                                    <th>Resource</th>
                                    <th>Production</th>
                                    <th>Consumption</th>
                                    <th>Excess</th>
                                    <th>Storage</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for resource in json_data["general_resources"] %}
                                    {% set resource_index = loop.index0 %}
                                    <tr>
                                        <td>{{ resource.name }}</td>
                                        <td>{{ nation.resource_production[resource.key] if resource.key in nation.resource_production else 0 }}</td>
                                        <td>{{ nation.resource_consumption[resource.key] if resource.key in nation.resource_consumption else 0 }}</td>
                                        {% set excess_value = nation.get("resource_excess", {}).get(resource["key"], 0) %}
                                        {% set storage = nation.get("resource_storage", {}).get(resource["key"], 0) %}
                                        {% set capacity = nation.get("nation_resource_capacity", {}).get(resource["key"], 0) %}
                                        {% if excess_value + storage < 0 %}
                                            <td class="excess-critical-negative">{{ excess_value }}</td>
                                        {% elif excess_value < 0 %}
                                            <td class="excess-negative">{{ excess_value }}</td>
                                        {% elif excess_value + storage > capacity %}
                                            <td class="excess-overflow">{{ excess_value }}</td>
                                        {% elif excess_value > 0 %}
                                            <td class="excess-positive">{{ excess_value }}</td>
                                        {% else %}
                                            <td>{{ excess_value }}</td>
                                        {% endif %}
                                        <td>{{ form.resource_storage[resource.key](class="form-control") }} / {{ nation.nation_resource_capacity[resource.key] if resource.key in nation.nation_resource_capacity else 0 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <thead>
                                <tr class="resources-header-row">
                                    <th colspan="6" class="resources-header">Unique Resources</th>
                                </tr>
                                <tr>
                                    <th>Resource</th>
                                    <th>Production</th>
                                    <th>Consumption</th>
                                    <th>Excess</th>
                                    <th>Storage</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for resource in json_data["unique_resources"] %}
                                    {% set resource_index = json_data["general_resources"]|length + loop.index0 %}
                                    <tr>
                                        <td>{{ resource.name }}</td>
                                        <td>{{ nation.resource_production[resource.key] if resource.key in nation.resource_production else 0 }}</td>
                                        <td>{{ nation.resource_consumption[resource.key] if resource.key in nation.resource_consumption else 0 }}</td>
                                        {% set excess_value = nation.get("resource_excess", {}).get(resource["key"], 0) %}
                                        {% set storage = nation.get("resource_storage", {}).get(resource["key"], 0) %}
                                        {% set capacity = nation.get("nation_resource_capacity", {}).get(resource["key"], 0) %}
                                        {% if excess_value + storage < 0 %}
                                            <td class="excess-critical-negative">{{ excess_value }}</td>
                                        {% elif excess_value < 0 %}
                                            <td class="excess-negative">{{ excess_value }}</td>
                                        {% elif excess_value + storage > capacity %}
                                            <td class="excess-overflow">{{ excess_value }}</td>
                                        {% elif excess_value > 0 %}
                                            <td class="excess-positive">{{ excess_value }}</td>
                                        {% else %}
                                            <td>{{ excess_value }}</td>
                                        {% endif %}
                                        <td>{{ form.resource_storage[resource.key](class="form-control") }} / {{ nation.nation_resource_capacity[resource.key] if resource.key in nation.nation_resource_capacity else 0 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <h2>Jobs</h2>
                    <section class="jobs-section">
                        <h2>Jobs</h2>
                        <p>Pop Count: {{ nation.pop_count }}</p>
                        <div class="table-wrapper">
                            <table class="jobs-table">
                                <thead>
                                    <tr>
                                        <th>Job</th>
                                        <th>Assigned Pops</th>
                                        <th>Upkeep</th>
                                        <th>Production</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for job, details in nation.job_details.items() %}
                                        <tr>
                                            <td>{{ details.display_name }}</td>
                                            <td>{{ form.jobs[job] }}</td>
                                            <td>
                                            {% if details.upkeep is defined %}
                                                {% for resource, cost in details.upkeep.items() %}
                                                    {{ resource }}: {{ cost }}<br>
                                                {% endfor %}
                                            {% endif %}
                                            </td>
                                            <td>
                                            {% if details.production is defined %}
                                                {% for resource, prod in details.production.items() %}
                                                    {{ resource }}: {{ prod }}<br>
                                                {% endfor %}
                                            {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </section>
                </div>
        </div>
        
        <!-- Districts Section -->
        <div class="expandable-section" id="districts-cities-wonders-section">
            <div class="section-header" onclick="toggleSection(this)">
                <h2>Districts, Cities, & Wonders</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <h2>Districts</h2>
                <p>{{ schema.properties.district_slots.label }}: {{ nation.district_slots }}</p>
                <div class="districts-grid">
                    {% for district_field in form.districts %}
                        <div class="district-slot">
                            <label for="{{ district_field.id }}">Slot {{ loop.index }}</label></br></br>
                            {{ district_field.form.type.label }}: {{ district_field.form.type(class="form-control") }}
                            {{ district_field.form.node.label }}: {{ district_field.form.node(class="form-control") }}
                        </div>
                    {% endfor %}
                </div>
                </br>

                {% if nation.get("empire", False) %}
                    <h2>Imperial District</h2>
                    <div class="district-slot">
                        {{ form.imperial_district.form.type.label }}: {{ form.imperial_district.form.type(class="form-control") }}
                        {{ form.imperial_district.form.node.label }}: {{ form.imperial_district.form.node(class="form-control") }}
                    </div>
                {% endif %}
                
                {% if nation.city_slots > 0 %}
                    <h2>Cities</h2>
                    <p>{{ schema.properties.city_slots.label }}: {{ nation.city_slots }}</p>
                    <div class="districts-grid">
                        {% for city_field in form.cities %}
                            <div class="district-slot">
                                <label for="{{ city_field.id }}">Slot {{ loop.index }}</label></br></br>
                                {{ city_field.form.name.label }}: {{ city_field.form.name(class="form-control") }}</br>                   
                                {{ city_field.form.type.label }}: {{ city_field.form.type(class="form-control") }}
                                {{ city_field.form.node.label }}: {{ city_field.form.node(class="form-control") }}
                                {{ city_field.form.wall.label }}: {{ city_field.form.wall(class="form-control") }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

				{% if "wonders" in linked_objects and linked_objects["wonders"] is not none %}
					<h2>Wonders</h2>
					<div class="table-wrapper">
						<table class="info-table">
							<thead>
								<tr class="resources-header-row">
									<th colspan="7" class="resources-header">Wonders</th>
								</tr>
								<tr>
									<th>Name</th>
									<th>Node</th>
									<th>Era Created</th>
									<th>Legacy Status</th>
								</tr>
							</thead>
							<tbody>
								{% for wonder in linked_objects["wonders"] %}
									<tr>
										<td><a href="{{ wonder['link'] }}">{{ wonder["name"] }}</a></td>
										
										<td>
											{% if wonder["node"] is not none %}
												{% set resource = find_dict_in_list(json_data["general_resources"], "key", wonder["node"]) %}
												{% if not resource %}
													{% set resource = find_dict_in_list(json_data["unique_resources"], "key", wonder["node"]) %}
												{% endif %}
												
												{% if resource and resource is not none %}
													{{ resource["name"] }}
												{% elif resource is none %}
													None
												{% else %}
													Unknown Resource ({{ wonder["node"] }})
												{% endif %}
											{% else %}
												None
											{% endif %}
										</td>
										
										<td>{{ wonder["era_created"] }}</td>
										
										<td>{{ wonder["legacy_status"] }}</td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>
				{% endif %}
            </div>
        </div>

        <!-- Tech Section -->
        <div class="expandable-section" id="tech-section">
            <div class="section-header" onclick="toggleSection(this)">
                <h2>Technologies</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                {{ render_tech_tree(nation.get("technologies", {"political_philosophy": {"researched": true}}), nation.get("technology_cost_modifier", 0), nation.get("technology_cost_minimum", 2), json_data, editable=True, form=form, form_json=form_json["technologies"]) }}
            </div>
        </div>

        <div class="expandable-section" id="progress-quests-section">
            <div class="section-header" onclick="toggleSection(this)">
                <h2>Progress Quests</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <div class="table-wrapper">
                    <table class="jobs-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Slot</th>
                                <th>Bonus Progress Per Tick</th>
                                <th>Current Progress</th>
                                <th>Required Progress</th>
                                <th>Link</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="progress_quests-tbody" class="sortable-tbody">
                            {% for quest_field in form.progress_quests %}
                                <tr class="sortable-row">
                                    <td>{{ quest_field.quest_name(class="form-control") }}</td>
                                    <td>{{ quest_field.slot(class="form-control") }}</td>
                                    <td>{{ quest_field.bonus_progress_per_tick(class="form-control") }}</td>
                                    <td>{{ quest_field.current_progress(class="form-control") }}</td>
                                    <td>{{ quest_field.required_progress(class="form-control") }}</td>
                                    <td>{{ quest_field.link(class="form-control") }}</td>
                                    <td>
                                        <span class="drag-handle">⋮⋮</span>
                                        <button type="button" class="btn btn-danger" onclick="removeFromArray(this, 'progress_quests')">Remove</button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <button type="button" class="btn btn-primary" onclick="addToArray('progress_quests')">Add Quest</button>
            </div>
        </div>


        <div class="expandable-section" id="modifiers-section">
            <div class="section-header" onclick="toggleSection(this)">
                <h2>Modifiers</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <table class="jobs-table">
                    <thead>
                        <tr>
                            <th>Field</th>
                            <th>Value</th>
                            <th>Duration</th>
                            <th>Source</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="modifiers-tbody">
                        {% for modifier_field in form.modifiers %}
                            <tr>
                                <td>{{ modifier_field.field(class="form-control") }}</td>
                                <td>{{ modifier_field.value(class="form-control") }}</td>
                                <td>{{ modifier_field.duration(class="form-control") }}</td>
                                <td>{{ modifier_field.source(class="form-control") }}</td>
                                <td><button type="button" class="btn btn-danger" onclick="removeFromArray(this)">Remove</button></td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <button type="button" class="btn btn-primary" onclick="addToArray('modifiers')">Add Modifier</button>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
        <script>
            // Initialize sortable for progress quests
            document.addEventListener('DOMContentLoaded', function() {
                const progressQuestsTbody = document.getElementById('progress_quests-tbody');
                if (progressQuestsTbody) {
                    new Sortable(progressQuestsTbody, {
                        handle: '.drag-handle',
                        animation: 150,
                        onEnd: function() {
                            reindexProgressQuests();
                        }
                    });
                }
            });

            function reindexProgressQuests() {
                const tbody = document.getElementById('progress_quests-tbody');
                Array.from(tbody.children).forEach((row, index) => {
                    row.querySelectorAll('select, input').forEach(input => {
                        const fieldName = input.name.split('-').pop();
                        input.name = `progress_quests-${index}-${fieldName}`;
                        input.id = `progress_quests-${index}-${fieldName}`;
                    });
                });
            }

            function addToArray(fieldName) {
                const tbody = document.getElementById(`${fieldName}-tbody`);
                const currentCount = tbody.children.length;
                
                const newRow = document.createElement('tr');
                if (fieldName === "progress_quests") {
                    newRow.className = 'sortable-row';
                }

                if (fieldName === "external_modifiers") {
                    const typeChoices = ["nation", "character"].map(choice => [choice, choice]);
                    
                    const typeOptions = typeChoices.map(([value, label]) => 
                        `<option value="${value}">${label}</option>`
                    ).join('');
                    
                    newRow.innerHTML = `
                        <td>
                            <select class="form-control" name="${fieldName}-${currentCount}-type" id="${fieldName}-${currentCount}-type">
                                ${typeOptions}
                            </select>
                        </td>
                        <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-modifier" id="${fieldName}-${currentCount}-modifier" required></td>
                        <td><input type="float" class="form-control" name="${fieldName}-${currentCount}-value" id="${fieldName}-${currentCount}-value" required></td>
                        <td><button type="button" class="btn btn-danger" onclick="removeFromArray(this, '${fieldName}')">Remove</button></td>
                    `;
                } else if (fieldName === "modifiers") {
                    newRow.innerHTML = `
                        <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-field" id="${fieldName}-${currentCount}-field" required></td>
                        <td><input type="float" class="form-control" name="${fieldName}-${currentCount}-value" id="${fieldName}-${currentCount}-value" required></td>
                        <td><input type="float" class="form-control" name="${fieldName}-${currentCount}-duration" id="${fieldName}-${currentCount}-duration" value="0" required></td>
                        <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-source" id="${fieldName}-${currentCount}-source" required></td>
                        <td><button type="button" class="btn btn-danger" onclick="removeFromArray(this, '${fieldName}')">Remove</button></td>
                    `;
                } else if (fieldName === "progress_quests") {
                    newRow.innerHTML = `
                        <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-quest_name" id="${fieldName}-${currentCount}-name" required></td>
                        <td>
                            <select class="form-control" name="${fieldName}-${currentCount}-slot" id="${fieldName}-${currentCount}-slot">
                                <option value="no_slot">No Slot</option>
                                <option value="tier_1_spell_slot">Tier 1 Spell Slot</option>
                                <option value="tier_2_spell_slot">Tier 2 Spell Slot</option>
                                <option value="tier_3_spell_slot">Tier 3 Spell Slot</option>
                            </select>
                        </td>
                        <td><input type="number" class="form-control" name="${fieldName}-${currentCount}-bonus_progress_per_tick" id="${fieldName}-${currentCount}-bonus_progress_per_tick" value="0" required></td>
                        <td><input type="number" class="form-control" name="${fieldName}-${currentCount}-current_progress" id="${fieldName}-${currentCount}-current_progress" value="0" required></td>
                        <td><input type="number" class="form-control" name="${fieldName}-${currentCount}-required_progress" id="${fieldName}-${currentCount}-required_progress" value="0" required></td>
                        <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-link" id="${fieldName}-${currentCount}-link" required></td>
                        <td>
                            <span class="drag-handle">⋮⋮</span>
                            <button type="button" class="btn btn-danger" onclick="removeFromArray(this, '${fieldName}')">Remove</button>
                        </td>
                    `;
                }

                tbody.appendChild(newRow);

                if (fieldName === "progress_quests") {
                    updateProgressQuestSlots();
                }
            }
        
            function removeFromArray(button, categoryName) {
                const row = button.closest('tr');
                row.remove();
                
                if (categoryName === 'progress_quests') {
                    reindexProgressQuests();
                } else {
                    // Reindex other arrays
                    const tbody = document.getElementById(`${categoryName}-tbody`);
                    Array.from(tbody.children).forEach((row, index) => {
                        row.querySelectorAll('select, input').forEach(input => {
                            const fieldName = input.name.split('-').pop();
                            input.name = `${categoryName}-${index}-${fieldName}`;
                            input.id = `${categoryName}-${index}-${fieldName}`;
                        });
                    });
                }
            }

            function resetConcessions() {
                const concessionsDiv = document.getElementById('concessions');
                concessionsDiv.textContent = '';
                
                // Clear the hidden input field
                const concessionsField = document.getElementById('concessions-field');
                concessionsField.value = JSON.stringify({});
            }

            // Add event listener to recenter tech tree when its section is expanded
            document.addEventListener('DOMContentLoaded', function() {
                const techSection = document.getElementById('tech-section');
                if (techSection) {
                    // Listen for section expansion
                    techSection.addEventListener('section:expanded', function() {
                        if (typeof recenterTechTree === 'function') {
                            recenterTechTree();
                        }
                    });
                    
                    // If section is already expanded on page load, recenter after a delay
                    if (techSection.classList.contains('section-expanded')) {
                        setTimeout(function() {
                            if (typeof recenterTechTree === 'function') {
                                recenterTechTree();
                            }
                        }, 500); // Longer delay to ensure everything is loaded
                    }
                }
            });

            // Add event listener for centralization law changes
            document.addEventListener('DOMContentLoaded', function() {
                const centralizationField = document.querySelector('select[name="centralization_law"]');
                if (centralizationField) {
                    centralizationField.addEventListener('change', function() {
                        updateProgressQuestSlots();
                    });
                }
            });

            function updateProgressQuestSlots() {
                // Get current centralization law
                const centralizationLaw = document.querySelector('select[name="centralization_law"]').value;

                // Define slot availability based on centralization law
                const slotAvailability = {
                    'Autonomous': { 'zero_slots': 4 },
                    'Decentralized': { 'one_slots': 3 },
                    'Standard': { 'two_slots': 2 },
                    'Focused': { 'three_slots': 1, 'one_slots': 1 },
                    'Absolute': { 'four_slots': 1 }
                };
                
                const availableSlots = slotAvailability[centralizationLaw] || {};
                
                // Update all progress quest slot dropdowns
                document.querySelectorAll('select[name*="progress_quests"][name*="slot"]').forEach(select => {
                    const currentValue = select.value;
                    
                    // Clear existing options
                    select.innerHTML = '<option value="no_slot">No Slot</option>';
                    
                    // Add progress slots based on centralization
                    if (availableSlots.zero_slots) {
                        select.innerHTML += '<option value="0_progress_slot">0 Progress Slot</option>';
                    }
                    if (availableSlots.one_slots) {
                        select.innerHTML += '<option value="1_progress_slot">1 Progress Slot</option>';
                    }
                    if (availableSlots.two_slots) {
                        select.innerHTML += '<option value="2_progress_slot">2 Progress Slot</option>';
                    }
                    if (availableSlots.three_slots) {
                        select.innerHTML += '<option value="3_progress_slot">3 Progress Slot</option>';
                    }
                    if (availableSlots.four_slots) {
                        select.innerHTML += '<option value="4_progress_slot">4 Progress Slot</option>';
                    }
                    
                    // Always add spell slots
                    select.innerHTML += '<option value="tier_1_spell_slot">Tier 1 Spell Slot</option>';
                    select.innerHTML += '<option value="tier_2_spell_slot">Tier 2 Spell Slot</option>';
                    select.innerHTML += '<option value="tier_3_spell_slot">Tier 3 Spell Slot</option>';
                    
                    // Restore previous value if still valid
                    if (Array.from(select.options).some(option => option.value === currentValue)) {
                        select.value = currentValue;
                    }
                });
            }
        </script>
        
        
        <!-- Reason and Submit -->
        <div class="form-actions">
            <div class="reason-field">
                <label for="{{ form.reason.id }}">{{ form.reason.label.text }}:</label>
                {{ form.reason(class="form-control") }}
            </div>
            
            <div class="button-group" style="text-align: left; margin-top: 10px;">
                {% if g.user.is_admin %}
                    <button type="submit" formaction="{{ request.path }}/save" class="btn btn-primary">Save</button>
                {% endif %}
                <button type="submit" formaction="{{ request.path }}/request" class="btn btn-secondary">Request</button>
            </div>
        </div>
    </form>
</div>
{% endblock %}
