{"$jsonSchema": {"bsonType": "object", "required": ["name", "market_head"], "preview": ["market_head", "primary_resource", "secondary_resource_one", "secondary_resource_two"], "laws": ["market_type", "tariff_stance", "tier"], "external_calculation_requirements": {"members": ["market_safety_stance"]}, "properties": {"name": {"bsonType": "string", "label": "Name", "description": "The name of the market"}, "market_type": {"bsonType": "enum", "label": "Market Type", "description": "The type of market", "enum": ["Commodity", "Illicit", "<PERSON><PERSON>", "Luxury", "Maritime", "Militant"], "laws": {"Commodity": {"member_stability_gain_chance_per_market_tier": 0.05}, "Illicit": {"trade_risk": 0.1, "minimum_trade_risk": -0.05}, "Hub": {"resource_storage_capacity": 5, "member_resource_discount_per_tier": 0.05, "owner_resource_discount_per_tier": 0.05}, "Luxury": {"generate_luxury_resources_per_market_tier": 1}, "Maritime": {}, "Militant": {}}}, "tariff_stance": {"bsonType": "enum", "label": "Tariff Stance", "description": "How many tariffs the market leader imposes", "enum": ["None", "Low", "Moderate", "High", "Extreme"], "laws": {"None": {"member_nation_trade_distance": 3, "member_nation_stability_gain_chance": 0.1}, "Low": {"owner_nation_money_income_per_market_tier_per_member": 3}, "Moderate": {"owner_nation_money_income_per_market_tier_per_member": 6, "member_nation_trade_distance": -3}, "High": {"owner_nation_money_income_per_market_tier_per_member": 15, "member_nation_trade_distance": -6, "member_nation_trade_slots": -3}, "Extreme": {"owner_nation_money_income_per_market_tier_per_member": 30, "member_nation_trade_distance": -9, "member_nation_trade_slots": -3, "member_nation_stability_loss_chance": 0.1}}}, "tier": {"bsonType": "enum", "label": "Tier", "description": "The tier of the market", "enum": ["I", "II", "III", "IV", "V"], "default": "I", "laws": {"I": {"primary_resource_production": 1, "resource_storage_capacity": 5, "market_tier_multiplier": 1}, "II": {"primary_resource_production": 2, "secondary_resource_production": 1, "resource_storage_capacity": 5, "market_tier_multiplier": 2}, "III": {"primary_resource_production": 3, "secondary_resource_production": 2, "resource_storage_capacity": 10, "market_tier_multiplier": 3}, "IV": {"primary_resource_production": 4, "secondary_resource_production": 2, "resource_storage_capacity": 15, "market_tier_multiplier": 4}, "V": {"primary_resource_production": 5, "secondary_resource_production": 3, "resource_storage_capacity": 15, "market_tier_multiplier": 5}}}, "primary_resource": {"bsonType": "json_resource_enum", "label": "Primary Resource", "description": "The primary resource of the market"}, "secondary_resource_one": {"bsonType": "json_resource_enum", "label": "Secondary Resource One", "description": "The first secondary resource of the market"}, "secondary_resource_two": {"bsonType": "json_resource_enum", "label": "Secondary Resource Two", "description": "The second secondary resource of the market"}, "market_head": {"bsonType": "linked_object", "label": "Market Head", "description": "The nation that currently heads up the market", "collections": ["nations", "merchants"]}, "resource_production": {"bsonType": "object", "label": "Resource Production", "description": "The amount of each resource the market makes at the start of each session", "calculated": true, "hidden": true}, "resource_storage": {"bsonType": "object", "label": "Resource Storage", "description": "The resource reserves of the market"}, "market_resource_capacity": {"bsonType": "object", "label": "Resource Storage Capacity", "description": "The resource storage capacity of the market", "calculated": true, "hidden": true}, "minimum_trade_risk": {"bsonType": "number", "label": "Minimum Trade Risk", "description": "The minimum risk of trading in the market", "format": "percentage", "calculated": true, "base_value": 0.05}, "trade_risk": {"bsonType": "number", "label": "Trade Risk", "description": "The risk of trading in the market", "format": "percentage", "calculated": true, "base_value": 0.05}, "members": {"bsonType": "array", "label": "Members", "description": "Nations currently members of this market", "linkCollection": "market_links", "linkQueryTarget": "market", "collections": ["nations"], "queryTarget": "member", "preview": ["market_safety_stance"], "items": {"bsonType": "object", "properties": {"member": {"bsonType": "linked_object", "label": "Member", "description": "The member of the market", "collections": ["nations"]}, "market_safety_stance": {"bsonType": "enum", "label": "Market Safety Stance", "description": "The stance the member has towards the market", "enum": ["None", "Exploit", "Abuse", "Ignore", "Protect", "Preserve"], "default": "Ignore"}}}}}}}