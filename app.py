import os
from app_core import app, mongo, discord, category_data, json_data
from routes import register_routes
from flask import request, redirect

register_routes(app, mongo, discord)

@app.before_request
def before_request():
    if not request.is_secure and not app.debug:
        url = request.url.replace("http://", "https://", 1)
        return redirect(url, code=301)

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 5000))
    app.run(host="0.0.0.0", port=port, debug=True)
