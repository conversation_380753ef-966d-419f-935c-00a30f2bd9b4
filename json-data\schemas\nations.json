{"$jsonSchema": {"title": "Nations", "bsonType": "object", "required": ["name", "region", "primary_race", "primary_culture", "primary_religion", "government_type", "succession_type", "foreign_acceptance", "origin"], "laws": ["stability", "government_type", "succession_type", "nomad_camp_type", "conscription_type", "military_funding", "magic_stance", "centralization_law", "citizenship_stance", "foreign_acceptance", "subject_stance", "land_doctrine", "naval_doctrine", "diplomatic_stance", "scientific_stance", "economy_type", "expansion_stance", "mercenary_law", "consumption_stance", "tax_stance", "justice_stance", "vassal_type"], "preview": ["region"], "external_calculation_requirements": {"overlord": {"fields": ["subject_stance"], "modifier_prefix": "vassal"}, "primary_race": ["positive_trait", "negative_trait"], "primary_religion": ["external_modifiers"], "wonders": ["external_modifiers", "node"], "region": ["external_modifiers"], "rulers": ["titles", "modifiers", {"artifacts": ["external_modifiers"]}], "pops": [], "vassals": {"fields": ["vassal_type"], "modifier_prefix": "overlord"}, "markets": {"fields": ["market_safety_stance", "tariff_stance", "market_type", "tier"], "modifier_prefix": "member"}, "owned_markets": {"fields": ["tariff_stance", "market_type", "tier"], "modifier_prefix": "owner"}}, "properties": {"name": {"bsonType": "string", "label": "Name", "description": "The nation's name"}, "empire": {"bsonType": "boolean", "label": "Empire", "description": "Whether the nation is an empire", "hidden": true}, "prestige": {"bsonType": "number", "label": "Prestige", "description": "The empire's current prestige", "hideIfNone": "empire"}, "prestige_gain": {"bsonType": "number", "label": "Prestige Gain", "description": "The amount of prestige the empire gains each session", "calculated": true}, "rulers": {"bsonType": "array", "label": "Rulers", "description": "The list of rulers of the nation", "collections": ["characters"], "queryTargetAttribute": "ruling_nation_org"}, "temperament": {"bsonType": "enum", "label": "Temperament", "description": "The nation's level of stability", "default": "Neutral", "enum": ["Player", "Neutral", "Friendly", "Hostile", "Withdrawn", "Curious", "Supremacist", "Zealous"], "view_access_level": 10}, "sessions_since_temperament_change": {"bsonType": "number", "label": "Sessions Since Temperament Change", "description": "The number of sessions since the nation's temperament was last changed", "default": 1, "hidden": true, "view_access_level": 10}, "temperament_change_roll": {"bsonType": "number", "label": "Temperament Change Roll", "description": "The roll that determined if the nation's temperament changed", "hidden": true, "view_access_level": 10}, "temperament_change_chance_at_tick": {"bsonType": "number", "label": "Temperament Change Chance At Tick", "description": "The chance the nation's temperament changed at the last tick", "hidden": true, "view_access_level": 10}, "temperament_roll": {"bsonType": "number", "label": "Temperament Roll", "description": "The roll that determined the nation's temperament", "hidden": true, "view_access_level": 10}, "temperament_odds": {"bsonType": "object", "label": "Temperament Odds", "description": "The odds of each temperament at the last temperament change", "hidden": true, "view_access_level": 10}, "region": {"bsonType": "linked_object", "label": "Region", "description": "The region the nation is in", "collections": ["regions"]}, "administration": {"bsonType": "number", "label": "Administration", "description": "The nation's level of administration", "calculated": true, "base_value": 1}, "effective_territory": {"bsonType": "number", "label": "Effective Territory", "description": "The nation's maximum territory they can hold before they start suffering penalties", "calculated": true, "effective_territory_per_admin": 5, "base_value": 10}, "current_territory": {"bsonType": "number", "label": "Current Territory", "description": "The amount of territory the nation currently holds", "calculated": true}, "territory_types": {"bsonType": "object", "label": "Territory Types", "description": "The types of territory the nation currently holds"}, "road_capacity": {"bsonType": "number", "label": "Road Capacity", "description": "The nation's maximum road capacity that can be used before they start suffering penalties", "calculated": true, "road_capacity_per_admin": 4, "base_value": 6}, "road_usage": {"bsonType": "number", "label": "Road Usage", "description": "The amount of road capacity the nation is currently using", "default": 0}, "passive_expansion_chance": {"bsonType": "number", "label": "Passive Expansion Chance", "description": "The nation's percentage chance of expanding into adjacent territory each tick", "calculated": true, "format": "percentage"}, "stability": {"bsonType": "enum", "label": "Stability", "description": "The nation's level of stability", "enum": ["<PERSON><PERSON><PERSON>", "Unsettled", "Balanced", "Stable", "United"], "default": "Balanced", "laws": {"Fragile": {"civil_war_chance": 0.2, "karma": -2}, "Unsettled": {"karma": -2}, "Balanced": {}, "Stable": {"stability_loss_chance": 0.15, "karma": 2}, "United": {"stability_loss_chance": 0.35, "karma": 2}}}, "stability_loss_chance_on_leader_death": {"bsonType": "number", "label": "Stability Loss Chance On Leader Death", "description": "The nation's percentage chance of losing a level of stability when their leader dies", "calculated": true, "format": "percentage"}, "vampirism_chance": {"bsonType": "number", "label": "Vampirism Chance", "description": "The nation's percentage chance of a vampire rising each tick", "calculated": true, "format": "percentage"}, "pop_loss_chance": {"bsonType": "number", "label": "Pop Loss Chance", "description": "The nation's percentage chance of losing a pop each tick", "calculated": true, "format": "percentage"}, "infamy": {"bsonType": "number", "label": "Infamy", "description": "The nation's current level of infamy", "default": 0}, "wars": {"bsonType": "array", "label": "Wars", "description": "The list of wars the nation is currently fighting, if any", "linkCollection": "war_links", "linkQueryTarget": "participant", "collections": ["wars"], "queryTarget": "war"}, "markets": {"bsonType": "array", "label": "Markets", "description": "The list of markets the nation is currently a part of, if any", "linkCollection": "market_links", "linkQueryTarget": "member", "collections": ["markets"], "queryTarget": "market", "preview": ["primary_resource", "secondary_resource_one", "secondary_resource_two"], "items": {}}, "owned_markets": {"bsonType": "array", "label": "Owned Markets", "description": "The list of markets the nation currently owns, if any", "collections": ["markets"], "queryTargetAttribute": "market_head", "preview": ["primary_resource", "secondary_resource_one", "secondary_resource_two"], "items": {}}, "factions": {"bsonType": "array", "label": "Factions", "description": "The list of factions currently based in the nation, if any", "collections": ["factions"], "queryTargetAttribute": "base"}, "hired_mercenaries": {"bsonType": "array", "label": "<PERSON><PERSON>", "description": "The list of mercenaries the nation has currently employed", "collections": ["mercenaries"], "queryTargetAttribute": "patron"}, "money": {"bsonType": "number", "label": "Money", "description": "The amount of money the nation currently has", "default": 0}, "money_capacity": {"bsonType": "number", "label": "Money Capacity", "description": "The amount of money the nation can hold", "calculated": true, "base_value": 6000}, "money_income": {"bsonType": "number", "label": "Money Income", "description": "The amount of money the nation gains each tick", "calculated": true, "base_value": 150}, "karma": {"bsonType": "number", "label": "Total Karma", "description": "The total amount of Karma the nation has", "calculated": true}, "rolling_karma": {"bsonType": "number", "label": "Rolling Karma", "description": "The amount of rolling karma the nation has.  Rolling karma is increased after getting a bad event, reduced after getting a good event, and reset at the start of each era", "default": 0}, "temporary_karma": {"bsonType": "number", "label": "Temporary Karma", "description": "The amount of temporary karma the nation has.  Temporary karma is removed after each tick", "default": 0}, "raw_roll": {"bsonType": "number", "label": "Raw Roll", "description": "The raw karma roll before modifiers", "hidden": true, "view_access_level": 10}, "event_roll": {"bsonType": "number", "label": "Event Roll", "description": "The roll that lead to the event's tier", "hidden": true, "view_access_level": 10}, "event_type": {"bsonType": "string", "label": "Event Type", "description": "The type of event that occurred", "hidden": true, "view_access_level": 10}, "migration_distance": {"bsonType": "number", "label": "Migration Distance", "description": "The distance this nation can migrate each session", "calculated": true, "base_value": 6}, "stationary_delay": {"bsonType": "number", "label": "Stationary Delay", "description": "The amount of sessions a nomadic nation can stay in once place before suffering penalties", "calculated": true, "base_value": 2}, "primary_race": {"bsonType": "linked_object", "label": "Primary Race", "description": "The primary race of the nation", "collections": ["races"]}, "primary_culture": {"bsonType": "linked_object", "label": "Primary Culture", "description": "The primary culture of the nation", "collections": ["cultures"]}, "primary_religion": {"bsonType": "linked_object", "label": "Primary Religion", "description": "The primary religion of the nation", "collections": ["religions"]}, "overlord": {"bsonType": "linked_object", "label": "Overlord", "description": "The overlord of the nation", "noneResult": "Independent", "collections": ["nations"], "hideIfNone": "overlord"}, "vassal_type": {"bsonType": "enum", "label": "Vassal Type", "description": "The type of vassal of the nation", "enum": ["None", "Tributary", "Protectorate", "Mercantile", "<PERSON>", "Provincial"], "hideIfNone": "overlord", "laws": {"None": {}, "Tributary": {}, "Protectorate": {"defensive_pact_slots_maximum": 1, "military_pact_slots_maximum": 0, "vassal_slots_maximum": 0}, "Mercantile": {"defensive_pact_slots_maximum": 0, "military_pact_slots_maximum": 0, "vassal_slots_maximum": 0, "money_income": -75, "overlord_nation_money_income": 75, "trade_slots": 3, "trade_distance": 3}, "Martial": {"defensive_pact_slots_maximum": 0, "military_pact_slots_maximum": 1, "vassal_slots_maximum": 0}, "Provincial": {"defensive_pact_slots_maximum": 0, "military_pact_slots_maximum": 0, "vassal_slots_maximum": 0, "overlord_nation_research_production": 1, "nation_max_research_production": 0}}}, "compliance": {"bsonType": "enum", "label": "Compliance", "description": "The vassal's current compliance", "enum": ["None", "<PERSON><PERSON><PERSON>", "Defiant", "Neutral", "Compliant", "Loyal"], "hideIfNone": "overlord"}, "disobey_chance": {"bsonType": "number", "label": "Chance to Disobey", "description": "Percentage chance for this nation to disobey a vassal action from their overlord", "calculated": true, "hideIfNone": "overlord", "format": "percentage"}, "rebellion_chance": {"bsonType": "number", "label": "Chance to Rebel", "description": "Percentage chance for this nation to Rebel against their overlord", "calculated": true, "hideIfNone": "overlord", "format": "percentage"}, "concessions_chance": {"bsonType": "number", "label": "Chance to Demand Concessions", "description": "Percentage chance for this nation to demand concessions from their overlord", "calculated": true, "hideIfNone": "overlord", "format": "percentage"}, "concessions_qty": {"bsonType": "number", "label": "Concessions Amount", "description": "Number of resources demanded each time concessions are demanded", "calculated": true, "base_value": 4, "hideIfNone": "overlord"}, "concessions": {"bsonType": "object", "label": "Concessions", "description": "The amount of concessions currently demanded by the vassal", "hideIfNone": "overlord"}, "vassals": {"bsonType": "array", "label": "<PERSON><PERSON><PERSON>", "description": "The list of vassals currently under the control of the nation", "collections": ["nations"], "queryTargetAttribute": "overlord", "preview": ["name", "compliance", "disobey_chance", "rebellion_chance", "concessions_chance", "concessions", "temperament"]}, "diplomatic_relations_1": {"bsonType": "array", "label": "Diplomatic Relations", "description": "The list of relations this nation has with other nations", "collections": ["diplo_relations"], "queryTargetAttribute": "nation_1", "preview": ["nation_2", "relation", "pact_type"]}, "diplomatic_relations_2": {"bsonType": "array", "label": "Diplomatic Relations", "description": "The list of relations this nation has with other nations", "collections": ["diplo_relations"], "queryTargetAttribute": "nation_2", "preview": ["nation_1", "relation", "pact_type"]}, "working_pop_count": {"bsonType": "number", "label": "Working Pops Count", "description": "The number of pops currently working jobs in the nation", "calculated": true}, "pop_count": {"bsonType": "number", "label": "Pop Count", "description": "The amount of pops the nation has", "calculated": true}, "effective_pop_capacity": {"bsonType": "number", "label": "Effective Pop Capacity", "description": "The amount of pops the nation can effectively hold", "calculated": true, "base_value": 13}, "pops": {"bsonType": "array", "label": "Pops", "description": "The list of pops in the nation", "collections": ["pops"], "queryTargetAttribute": "nation", "preview": ["race", "culture", "religion"], "sort_by": ["race", "culture", "religion"]}, "unique_minority_count": {"bsonType": "number", "label": "Minority Count", "description": "The amount of unique minorities the nation has", "calculated": true}, "stability_gain_chance": {"bsonType": "number", "label": "Stability Gain Chance", "description": "The nation's percentage chance of gaining a level of stability each tick", "calculated": true, "format": "percentage"}, "stability_loss_chance": {"bsonType": "number", "label": "Stability Loss Chance", "description": "The nation's percentage chance of losing a level of stability each tick", "calculated": true, "format": "percentage"}, "civil_war_chance": {"bsonType": "number", "label": "Civil War Chance", "description": "The nation's percentage chance of experiencing a civil war each tick", "calculated": true, "format": "percentage"}, "district_slots": {"bsonType": "number", "label": "District Slots", "description": "The amount of districts the nation can have", "calculated": true, "base_value": 3}, "government_type": {"bsonType": "enum", "label": "Government Type", "description": "The type of government of the nation", "enum": ["Autocracy", "Representative Council", "City State", "Trade Federation", "Archivist State", "Untamed Lords", "Far Travelers", "Iron-Fisted Dictatorship", "Imperial Council", "Sacred Vicar", "Great Khanate", "World Warden"], "laws": {"Autocracy": {"district_slots": 1, "repeat_stability_loss": 0.5, "city_slots": 3}, "Representative Council": {"mimimum_weaknesses": -1, "maximum_weaknesses": 1, "city_slots": 3}, "City State": {"defensive_pact_slots": 1, "effective_territory": -2, "city_slots": 1}, "Trade Federation": {"trade_slots_per_admin": 1, "city_slots": 4}, "Archivist State": {"administration_per_research_production": 3, "max_administration_per_research_production": 3, "locks_bureaucrat": true, "effective_territory": -2, "city_slots": 1}, "Untamed Lords": {"effective_territory": 6, "nomadic": 1, "hunter_food_production": 1}, "Far Travelers": {"effective_territory": 3, "nomadic": 1, "hunter_food_production": 1}, "Iron-Fisted Dictatorship": {"resource_production": 1, "high_stability_strength": 2, "low_stability_strength": -2, "city_slots": 5}, "Imperial Council": {"unit_money_production": 30, "unit_trade_slots": 1, "unit_trade_range": 1, "city_slots": 5}, "Sacred Vicar": {"city_slots": 5, "stability_gain_chance_per_foreign_religious_pop": 0.01, "max_stability_gain_chance_per_foreign_religious_pop": 0.5}, "Great Khanate:": {"morale": 2, "effective_territory_per_tributary": 6, "nomadic": 1, "hunter_food_production": 1}, "World Warden": {"migration_distance": 6, "effective_territory": 3, "nomadic": 1, "hunter_food_production": 1}}}, "nomadic": {"bsonType": "int", "label": "Nomadic", "description": "Whether the nation is nomadic", "calculated": true}, "succession_type": {"bsonType": "enum", "label": "Succession Type", "description": "The way the next leader of the nation is selected", "enum": ["Strength", "Inherited", "Elected"], "laws": {"Strength": {"stability_loss_chance_on_leader_death": 1}, "Inherited": {"stability_loss_chance_on_leader_death": 0.75}, "Elected": {"stability_loss_chance_on_leader_death": 0.5}}}, "nomad_camp_type": {"bsonType": "enum", "label": "Nomad Camp Type", "description": "The type of camp a nomadic nation currently has", "enum": ["None", "Caravan", "<PERSON><PERSON><PERSON>", "Encampment", "Hearth"], "laws": {"None": {}, "Caravan": {"trade_slots": 3, "money_production": 120, "strength": -1, "morale": -1}, "Canvass": {"production_of_available_nodes": 1}, "Encampment": {"strength": 1, "unit_morale": 1, "unit_mount_consumption": -1, "chance_to_lose_stability": 0.2, "chance_to_lose_stability_while_at_war": -0.1}, "Hearth": {"food_consumption": -3, "hunter_food_production": 1, "hunter_food_production_from_dock_or_farm": 1, "resource_production": -1, "food_production": 1, "research_production": 1}}}, "conscription_type": {"bsonType": "enum", "label": "Conscription Type", "description": "How much of the nation's population can be mobilized during a war", "enum": ["Low", "Moderate", "High", "Very High", "Total", "Rebellion"], "default": "Moderate", "laws": {"Moderate": {"recruit_percentage": 0.3}, "Low": {"recruit_percentage": 0.2, "strength": 1, "karma": 1}, "High": {"recruit_percentage": 0.4, "strength": -1}, "Very High": {"recruit_percentage": 0.5, "strength": -1, "karma": -2}, "Total": {"recruit_percentage": 0.65, "strength": -2, "karma": -2}, "Rebellion": {"recruit_percentage": 0.5}}}, "military_funding": {"bsonType": "enum", "label": "Military Funding", "description": "How well funded the nation's military is", "enum": ["Minimal", "Substandard", "Standard", "Notable", "Substantial", "Extreme"], "default": "Standard", "laws": {"Minimal": {"money_income": 150, "strength": -2}, "Substandard": {"money_income": 75, "strength": -1}, "Standard": {}, "Notable": {"money_income": -75, "unit_money_upkeep": 15, "strength": 1}, "Substantial": {"money_income": -150, "unit_money_upkeep": 30, "strength": 2}, "Extreme": {"money_income": -225, "unit_money_upkeep": 45, "strength": 3}}}, "magic_stance": {"bsonType": "enum", "label": "Magic Stance", "description": "The nation's stance towards magic", "enum": ["Bureaucracy", "Creation", "Military", "Persecution", "Manifest"], "laws": {"Bureaucracy": {"administrator_magic_production": 0.3333333333333334}, "Creation": {}, "Military": {}, "Persecution": {"cannot_create_magical_units": true}, "Manifest": {"karma_per_magic_node": 2, "magic_node_value": -1}}}, "centralization_law": {"bsonType": "enum", "label": "Centralization Law", "description": "The degree to which the nation is centralized", "enum": ["Autonomous", "Decentralized", "Standard", "Focused", "Absolute"], "default": "Standard", "laws": {"Autonomous": {"0_progress_slots": 4}, "Decentralized": {"1_progress_slots": 3}, "Standard": {"2_progress_slots": 2}, "Focused": {"3_progress_slots": 1, "1_progress_slots": 1}, "Absolute": {"4_progress_slots": 1}}}, "citizenship_stance": {"bsonType": "enum", "label": "Citizenship Stance", "description": "How easy it is to get citizenship in the nation", "enum": ["None", "Free", "Open", "Restricted", "Closed"], "laws": {"None": {}, "Free": {"stability_loss_chance": 0.05}, "Open": {"stability_loss_chance": 0.05}, "Restricted": {"stability_loss_chance": -0.1}, "Closed": {"stability_loss_chance": -0.2}}}, "foreign_acceptance": {"bsonType": "enum", "label": "Foreign Acceptance", "description": "How minority cultures and religions living in the nation are handled", "enum": ["Oppression", "Integration", "Acceptance", "Harmony"], "default": "Integration", "laws": {"Oppression": {"stability_loss_chance_per_unique_minority": 0.05, "max_stability_loss_chance_per_unique_minority": 0.5, "homogeneous_stability_loss_chance": -0.25}, "Integration": {"stability_loss_chance_per_unique_minority": 0.03, "max_stability_loss_chance_per_unique_minority": 0.3}, "Acceptance": {"stability_gain_chance_per_unique_minority": 0.02, "max_stability_gain_chance_per_unique_minority": 0.3}, "Harmony": {"stability_gain_chance_per_unique_minority": 0.05, "max_stability_gain_chance_per_unique_minority": 0.3}}}, "subject_stance": {"bsonType": "enum", "label": "Subject Stance", "description": "The way the nation tends to interact with its vassals", "enum": ["Suppression", "Balanced", "Benevolence"], "default": "Balanced", "laws": {"Suppression": {"vassal_nation_concessions_chance": -1}, "Balanced": {}, "Benevolence": {"vassal_nation_rebellion_chance_above_rebellious": -1, "vassal_nation_concessions_qty_mult": 2}}}, "land_doctrine": {"bsonType": "enum", "label": "Land Doctrine", "description": "The nation's general approach to land based warfare", "enum": ["Standard", "Nomadic", "Shock", "Volley", "Safeguard", "Militia", "Ambush"], "laws": {"Standard": {}, "Nomadic": {"land_unit_speed": 1}, "Shock": {"first_session_land_attack": 2, "after_first_session_land_defense": -4}, "Volley": {"land_unit_speed_mult": 0.5, "land_unit_range_when_stationary": 1}, "Safeguard": {"land_defense_against_ranged": 2, "defensive_war_land_defense": 1, "offensive_war_land_attack": -4}, "Militia": {}, "Ambush": {"land_attack_vs_damaged_units": 2, "land_non_ruler_unit_speed": 1, "land_cavalry_unit_speed": -1, "land_unit_hp_mult": 0.75}}}, "naval_doctrine": {"bsonType": "enum", "label": "Naval Doctrine", "description": "The nation's general approach to aquatic warfare", "enum": ["Standard", "Skirmish", "Fleet", "Transport"], "laws": {"Standard": {}, "Skirmish": {}, "Fleet": {}, "Transport": {}}}, "diplomatic_stance": {"bsonType": "enum", "label": "Diplomatic Stance", "description": "The nation's default stance towards interacting with its neighbors", "enum": ["Expansionist", "Cooperative", "Belligerent", "Isolated"], "laws": {"Cooperative": {"defensive_pact_slots": 1}, "Expansionist": {"effective_territory": 3, "nomad_effective_territory": 3}, "Belligerent": {"unit_recruitment_discount": 0.05, "unit_recruitment_discount_during_offensive_war": 0.05}, "Isolated": {"defensive_pact_slots": -1}}}, "scientific_stance": {"bsonType": "enum", "label": "Scientific Stance", "description": "The degree to which the nation is geared towards technological development", "enum": ["Puritanical", "Conservative", "Pragmatic", "Progressive", "Radical", "Revolutionary"], "default": "Pragmatic", "laws": {"Puritanical": {"karma": 4, "stability_gain_chance": 0.25, "locks_researcher": true}, "Conservative": {"karma": 2, "stability_gain_chance": 0.1}, "Pragmatic": {"research_production": 1}, "Progressive": {"research_production": 2, "stability_loss_chance_per_pop": 0.02, "karma": -2}, "Radical": {"research_production": 4, "stability_loss_chance_per_pop": 0.05, "karma": -4}, "Revolutionary": {"research_production": 6, "stability_loss_chance_per_pop": 0.05, "karma": -4, "ignore_nodes": 1}}}, "economy_type": {"bsonType": "enum", "label": "Economy Type", "description": "The direction the nation's economy is specialized in", "enum": ["None", "Conquest", "Construction", "Production", "Commerce"], "laws": {"None": {}, "Conquest": {"harvester_resource_production": -1, "miner_resource_production": -1}, "Construction": {"district_resource_consumption": -1, "district_pop_requirement": -1}, "Production": {"harvester_stone_upkeep": -1, "miner_wood_upkeep": -1, "resource_storage_capacity": 5}, "Commerce": {"stability_gain_chance_per_road_usage": 0.01, "maximum_markets": 1}}}, "expansion_stance": {"bsonType": "enum", "label": "Expansion Control Stance", "description": "The nation's default stance towards unregulated expansion", "enum": ["Limitless", "Free", "Restricted", "Controlled"], "default": "Free", "laws": {"Limitless": {"passive_expansion_chance": 0.6}, "Free": {"passive_expansion_chance": 0.4}, "Restricted": {"passive_expansion_chance": 0.3, "stability_loss_chance": 0.1}, "Controlled": {"passive_expansion_chance": 0.2, "stability_loss_chance": 0.2}}}, "mercenary_law": {"bsonType": "enum", "label": "Mercenary Law", "description": "The degree to which the nation's military is geared to employ mercenaries", "enum": ["No Mercenaries", "Some Mercenaries", "Mercenary Reliance", "Mercenary Military"], "default": "Some Mercenaries", "laws": {"No Mercenaries": {"mercenary_limit_mult": 0}, "Some Mercenaries": {"mercenary_limit_mult": 0.5}, "Mercenary Reliance": {"mercenary_limit_mult": 0.75, "strength": -1}, "Mercenary Military": {"mercenary_limit_mult": 1.25, "strength": -2, "mercenary_strength": 1}}}, "consumption_stance": {"bsonType": "enum", "label": "Consumption Stance", "description": "The nation's stance towards whether food should be distributed in abundance or rationed", "enum": ["None", "Indulgence", "Comfort", "Standard", "Rationing"], "laws": {"None": {}, "Indulgence": {"food_consumption_per_pop": 0.5, "stability_gain_chance": 0.4, "unit_morale": 1, "unit_hp": 1, "unit_retaliation_damage": 1}, "Comfort": {"food_consumption_per_pop": 0.3333333333333334, "stability_gain_chance": 0.25, "unit_morale": 1, "unit_hp": 1}, "Standard": {"stability_gain_chance": 0.05}, "Rationing": {"food_consumption_per_pop": -0.25, "stability_loss_chance": 0.15, "unit_morale": -1}}}, "tax_stance": {"bsonType": "enum", "label": "Tax Stance", "description": "The nation's tax rate", "enum": ["None", "Subsidized", "No Taxes", "Low", "Moderate", "High", "Oppressive"], "laws": {"None": {}, "Subsidized": {"money_income_per_pop": -18, "stability_gain_chance_per_pop": 0.01}, "No Taxes": {"money_income_per_pop": -9, "stability_gain_chance_per_pop": 0.01}, "Low": {"money_income_per_pop": 6}, "Moderate": {"money_income_per_pop": 12, "stability_loss_chance_per_pop": 0.01}, "High": {"money_income_per_pop": 18, "stability_loss_chance_per_pop": 0.02}, "Oppressive": {"money_income_per_pop": 24, "stability_loss_chance_per_pop": 0.03}}}, "justice_stance": {"bsonType": "enum", "label": "Justice <PERSON>", "description": "The nation's stance on handling criminals", "enum": ["None", "Reformation", "Standard", "Punishment"], "laws": {"None": {}, "Reformation": {"stability_gain_chance_per_positive_karma": 0.03, "max_stability_gain_chance_per_positive_karma": 0.3}, "Standard": {}, "Punishment": {"stability_loss_chance_per_negative_karma": 0.05, "max_stability_loss_chance_per_negative_karma": 0.5}}}, "city_slots": {"bsonType": "number", "label": "City Slots", "description": "The number of cities the nation's government can support without penalties", "calculated": true}, "city_count": {"bsonType": "number", "label": "City Count", "description": "The number of cities the nation currently controls", "calculated": true}, "defensive_pact_slots": {"bsonType": "number", "label": "Defensive Pact Slots", "description": "The number of defensive pacts the nation can support", "calculated": true, "base_value": 1}, "military_alliance_slots": {"bsonType": "number", "label": "Military Alliance Slots", "description": "The number of military alliances the nation can support", "calculated": true, "base_value": 1}, "vassal_slots": {"bsonType": "number", "label": "Vassal Slots", "description": "The number of vassals the nation can support", "calculated": true, "base_value": 5}, "non_aggression_pact_slots": {"bsonType": "number", "label": "Non-Aggression Pact Slots", "description": "The number of non-aggression pacts the nation can support", "calculated": true, "base_value": 5}, "origin": {"bsonType": "enum", "label": "Origin", "description": "The origin the nation selected at creation", "enum": ["Unknown", "(T) De<PERSON>ult", "(T) Fragile Unification", "(T) Fragmented", "(T) Strange Outsiders", "(T) Survivors", "(T) Fortunate", "(A) De<PERSON>ult", "(A) Conquest State", "(A) Protected Scion", "(A) Artificer State", "(A) Recent Subjugation", "(C) Aspiring Allies", "(C) Shattered Dominion", "(C) Enlightened Defeat", "(C) Loyal Vassal", "(C) Fleeing Victims", "(C) Local Bully"]}, "jobs": {"bsonType": "array", "label": "Jobs", "description": "The jobs that the pops of the nation are assigned to"}, "land_unit_count": {"bsonType": "number", "label": "Land Unit Count", "description": "The number of land units the nation has recruited", "calculated": true}, "land_unit_capacity": {"bsonType": "number", "label": "Land Unit Capacity", "description": "The number of land units the nation can field at any given time", "calculated": true}, "land_units": {"bsonType": "array", "label": "Land Units", "description": "The land units the nation has recruited"}, "naval_unit_count": {"bsonType": "number", "label": "Naval Unit Count", "description": "The number of naval units the nation has recruited", "calculated": true}, "naval_unit_capacity": {"bsonType": "number", "label": "Naval Unit Capacity", "description": "The number of naval units the nation can field at any given time", "calculated": true}, "naval_units": {"bsonType": "array", "label": "Naval Units", "description": "The naval units the nation has recruited"}, "land_attack": {"bsonType": "number", "label": "Land Attack Modifier", "description": "The land attack modifier of the nation", "calculated": true}, "land_defense": {"bsonType": "number", "label": "Land Defense Modifier", "description": "The land defense modifier of the nation", "calculated": true}, "naval_attack": {"bsonType": "number", "label": "Naval Attack Modifier", "description": "The naval attack modifier of the nation", "calculated": true}, "naval_defense": {"bsonType": "number", "label": "Naval Defense Modifier", "description": "The naval defense modifier of the nation", "calculated": true}, "mercenary_land_attack": {"bsonType": "number", "label": "Mercenary Land Attack Modifier", "description": "The mercenary land attack modifier of the nation", "calculated": true}, "mercenary_land_defense": {"bsonType": "number", "label": "Mercenary Land Defense Modifier", "description": "The mercenary land defense modifier of the nation", "calculated": true}, "mercenary_naval_attack": {"bsonType": "number", "label": "Mercenary Naval Attack Modifier", "description": "The mercenary naval attack modifier of the nation", "calculated": true}, "mercenary_naval_defense": {"bsonType": "number", "label": "Mercenary Naval Defense Modifier", "description": "The mercenary naval defense modifier of the nation", "calculated": true}, "resource_production": {"bsonType": "object", "label": "Resource Production", "description": "The gross resource production of the nation", "calculated": true}, "resource_consumption": {"bsonType": "object", "label": "Resource Consumption", "description": "The resource consumption of the nation", "calculated": true}, "resource_excess": {"bsonType": "object", "label": "Resource Excess", "description": "The net resource production of the nation", "calculated": true}, "resource_storage": {"bsonType": "object", "label": "Resource Storage", "description": "The resource reserves of the nation"}, "nation_resource_capacity": {"bsonType": "object", "label": "Resource Capacity", "description": "The resource reserves of the nation", "calculated": true}, "luxury_resources": {"bsonType": "array", "label": "Luxury Resources", "description": "The luxury resources the nation has in their lands", "items": {"bsonType": "string"}}, "technologies": {"bsonType": "array", "label": "Technologies", "description": "The technologies the nation has access to", "items": {"bsonType": "object", "properties": {"invested": {"bsonType": "number", "label": "Invested", "description": "The amount of research points already invested in the technology"}, "investing": {"bsonType": "number", "label": "Investing", "description": "The amount of research points being invested in the technology this turn"}, "researched": {"bsonType": "boolean", "label": "Researched", "description": "Whether the technology has been researched"}, "cost": {"bsonType": "number", "label": "Cost", "description": "The cost of the technology"}}}}, "technology_cost_modifier": {"bsonType": "number", "label": "Technology Cost Modifier", "description": "The flat modifier to the cost of technologies", "calculated": true}, "technology_cost_minimum": {"bsonType": "number", "label": "Technology Cost Minimum", "description": "The minimum cost of technologies", "calculated": true}, "districts": {"bsonType": "array", "label": "Districts", "description": "The districts the nation owns", "max_length": "district_slots", "items": {"bsonType": "object", "properties": {"type": {"bsonType": "json_district_enum", "label": "District Type", "description": "The type of the district"}, "node": {"bsonType": "string", "label": "District Node", "description": "The node under the district"}}}}, "imperial_district": {"bsonType": "object", "label": "Imperial District", "description": "The empire's current imperial district", "properties": {"type": {"bsonType": "json_district_enum", "label": "District Type", "description": "The type of the district"}, "node": {"bsonType": "string", "label": "District Nodes", "description": "The nodes under the district"}}}, "cities": {"bsonType": "array", "label": "Cities", "description": "The cities the nation owns", "max_length": "city_slots", "items": {"bsonType": "object", "properties": {"name": {"bsonType": "string", "label": "City Name", "description": "The name of the city"}, "type": {"bsonType": "string", "label": "City Type", "description": "The type of the city"}, "nodes": {"bsonType": "array", "label": "City Nodes", "description": "The nodes under the city", "items": {"bsonType": "string", "label": "Node Type", "description": "The type of the node"}}, "walls": {"bsonType": "string", "label": "City Walls", "description": "The type of walls the city has"}}}}, "wonders": {"bsonType": "array", "label": "Wonders", "description": "The list of wonders the nation currently owns", "collections": ["wonders"], "queryTargetAttribute": "owner_nation", "items": {"bsonType": "linked_object"}, "preview": ["name", "node", "era_created", "legacy_status"]}, "export_slots": {"bsonType": "number", "label": "Export Slots", "description": "The total number of export slots the nation has available each session, before accounting for ongoing trades", "calculated": true, "base_value": 3}, "import_slots": {"bsonType": "number", "label": "Import Slots", "description": "The total number of import slots the nation has available each session, before accounting for ongoing trades", "calculated": true, "base_value": 3}, "remaining_export_slots": {"bsonType": "number", "label": "Remaining Export Slots", "description": "The number of export slots the nation has available this session, after accounting for ongoing trades", "calculated": true}, "remaining_import_slots": {"bsonType": "number", "label": "Remaining Import Slots", "description": "The number of import slots the nation has available this session, after accounting for ongoing trades", "calculated": true}, "trade_distance": {"bsonType": "number", "label": "Trade Distance", "description": "The distance at which the nation can trade with other nations or merchant companies", "calculated": true, "base_value": 7}, "outgoing_trades": {"bsonType": "array", "label": "Outgoing Trades", "description": "The ongoing trades where the nation is currently exporting resources", "collections": ["trades"], "queryTargetAttribute": "exporting_nation", "items": {"bsonType": "linked_object"}}, "incoming_trades": {"bsonType": "array", "label": "Incoming Trades", "description": "The ongoing trades where the nation is currently importing resources", "collections": ["trades"], "queryTargetAttribute": "importing_nation", "items": {"bsonType": "linked_object"}}, "0_progress_slots": {"bsonType": "number", "label": "0 Progress Slots", "description": "The number of 0 progress slots the nation has available", "calculated": true}, "1_progress_slots": {"bsonType": "number", "label": "1 Progress Slots", "description": "The number of 1 progress slots the nation has available", "calculated": true}, "2_progress_slots": {"bsonType": "number", "label": "2 Progress Slots", "description": "The number of 2 progress slots the nation has available", "calculated": true}, "3_progress_slots": {"bsonType": "number", "label": "3 Progress Slots", "description": "The number of 3 progress slots the nation has available", "calculated": true}, "4_progress_slots": {"bsonType": "number", "label": "4 Progress Slots", "description": "The number of 4 progress slots the nation has available", "calculated": true}, "progress_quests": {"bsonType": "array", "label": "Progress Quests", "description": "The ongoing quests the nation is currently working on", "items": {"bsonType": "object", "properties": {"quest_name": {"bsonType": "string", "label": "Quest Name", "description": "The name of the quest"}, "progress_per_tick": {"bsonType": "number", "label": "Progress Per Tick", "description": "The amount of progress the nation makes on the quest each tick"}, "current_progress": {"bsonType": "number", "label": "Progress", "description": "The amount of progress the nation has made on the quest"}, "required_progress": {"bsonType": "number", "label": "Required Progress", "description": "The amount of progress required to complete the quest"}, "link": {"bsonType": "string", "label": "Link", "description": "The link to the quest"}}}}, "modifiers": {"bsonType": "array", "label": "Ongoing Modifiers", "description": "The ongoing modifiers currently affecting this nation", "items": {"bsonType": "object", "properties": {"key": {"bsonType": "string", "label": "Modifier Key", "description": "The key of the modifier"}, "value": {"bsonType": "number", "label": "Modifier Value", "description": "The value of the modifier"}, "duration": {"bsonType": "number", "label": "Modifier Duration", "description": "The duration of the modifier"}, "source": {"bsonType": "string", "label": "Modifier Source", "description": "The source of the modifier"}}}}}}}