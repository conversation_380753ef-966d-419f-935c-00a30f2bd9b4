{% extends "layout.html" %}

{% block title %}Edit {{ title }}{% endblock %}

{% block content %}
<div class="container">
	<a href="{{ request.path.replace('edit', 'item') }}">Back to View</a></br>
	{% if g.user.is_admin %}
		{% set default_action = request.path.replace('edit', 'clone') ~ '/save' %}
	{% else %}
		{% set default_action = request.path.replace('edit', 'clone') ~ '/request' %}
	{% endif %}
	<form method="POST" action="{{ default_action }}">
		<label for="reason">Reason</label>
		<input type="text" id="reason" name="reason" value="" required >
		{% if g.user.is_admin %}
			<button type="submit" formaction="{{ request.path.replace('edit', 'clone') }}/save">Immediate Clone</button>
		{% endif %}
		<button type="submit" formaction="{{ request.path.replace('edit', 'clone') }}/request">Request Clone</button>
	</form>
	<h1>{{ title }}:</h1>
	{% if g.user.is_admin %}
		{% set default_action = request.path ~ '/save' %}
	{% else %}
		{% set default_action = request.path ~ '/request' %}
	{% endif %}
	<form method="POST" action="{{ default_action }}">
		{{ form.csrf_token }}
		<div class="table-wrapper">
			<table class="info-table">
				{% for field in form if field.name != 'csrf_token' and field.name != 'submit' and field.name != 'reason' %}
					<tr>
						<th><label for="{{ field.id }}">{{ field.label.text }}</label></th>
						<td>
							{% if field.type == 'FieldList' and field.name == 'external_modifiers' %}
								<table class="modifiers-table">
									<thead>
										<tr>
											<th>Type</th>
											<th>Modifier</th>
											<th>Value</th>
											<th>Actions</th>
										</tr>
									</thead>
									<tbody id="{{ field.name }}-tbody">
										{% for modifier_field in field %}
											<tr>
												<td>{{ modifier_field.form.type(class="form-control") }}</td>
												<td>{{ modifier_field.form.modifier(class="form-control") }}</td>
												<td>{{ modifier_field.form.value(class="form-control") }}</td>
												<td><button type="button" class="btn btn-danger" onclick="removeFromArray(this, '{{ field.name }}')">Remove</button></td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
								<button type="button" class="btn btn-primary" onclick="addToArray('{{ field.name }}')">Add Modifier</button>
							{% elif field.type == 'FieldList' and field.name == 'modifiers' %}
								<table class="modifiers-table">
									<thead>
										<tr>
											<th>Field</th>
											<th>Value</th>
											<th>Duration</th>
											<th>Source</th>
											<th>Actions</th>
										</tr>
									</thead>
									<tbody id="{{ field.name }}-tbody">
										{% for modifier_field in field %}
											<tr>
												<td>{{ modifier_field.form.field(class="form-control") }}</td>
												<td>{{ modifier_field.form.value(class="form-control") }}</td>
												<td>{{ modifier_field.form.duration(class="form-control") }}</td>
												<td>{{ modifier_field.form.source(class="form-control") }}</td>
												<td><button type="button" class="btn btn-danger" onclick="removeFromArray(this, '{{ field.name }}')">Remove</button></td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
								<button type="button" class="btn btn-primary" onclick="addToArray('{{ field.name }}')">Add Modifier</button>
							{% elif field.type == 'FieldList' and field.name == 'progress_quests' %}
								<table class="modifiers-table">
									<thead>
										<tr>
											<th>Name</th>
											<th>Slot</th>
											<th>Bonus Progress Per Tick</th>
											<th>Current Progress</th>
											<th>Required Progress</th>
											<th>Link</th>
											<th>Actions</th>
										</tr>
									</thead>
									<tbody id="{{ field.name }}-tbody" class="sortable-tbody">
										{% for quest_field in field %}
											<tr class="sortable-row">
												<td>{{ quest_field.form.quest_name(class="form-control") }}</td>
												<td>{{ quest_field.form.slot(class="form-control") }}</td>
												<td>{{ quest_field.form.bonus_progress_per_tick(class="form-control") }}</td>
												<td>{{ quest_field.form.current_progress(class="form-control") }}</td>
												<td>{{ quest_field.form.required_progress(class="form-control") }}</td>
												<td>{{ quest_field.form.link(class="form-control") }}</td>
												<td>
													<span class="drag-handle">⋮⋮</span>
													<button type="button" class="btn btn-danger" onclick="removeFromArray(this, '{{ field.name }}')">Remove</button>
												</td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
								<button type="button" class="btn btn-primary" onclick="addToArray('{{ field.name }}')">Add Quest</button>
							{% else %}
								{{ field(class="form-control") }}
							{% endif %}
							
							{% if field.errors %}
								<ul class="errors">
									{% for error in field.errors %}
										<li>{{ error }}</li>
									{% endfor %}
								</ul>
							{% endif %}
							{% if field.description %}
								<small class="form-text text-muted">{{ field.description }}</small>
							{% endif %}
						</td>
					</tr>
				{% endfor %}
				<tr>
					<th><label for="{{ form.reason.id }}">{{ form.reason.label.text }}</label></th>
					<td>{{ form.reason(class="form-control") }}</td>
				</tr>
			</table>
		</div>
		
		{% if g.user.is_admin %}
			<button type="submit" formaction="{{ request.path }}/save">Save</button>
		{% endif %}
        <button type="submit" formaction="{{ request.path }}/request">Request</button>
    </form>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    // Initialize sortable for progress quests
    document.addEventListener('DOMContentLoaded', function() {
        const progressQuestsTbody = document.getElementById('progress_quests-tbody');
        if (progressQuestsTbody) {
            new Sortable(progressQuestsTbody, {
                handle: '.drag-handle',
                animation: 150,
                onEnd: function() {
                    reindexProgressQuests();
                }
            });
        }
    });

    function reindexProgressQuests() {
        const tbody = document.getElementById('progress_quests-tbody');
        Array.from(tbody.children).forEach((row, index) => {
            row.querySelectorAll('select, input').forEach(input => {
                const fieldName = input.name.split('-').pop();
                input.name = `progress_quests-${index}-${fieldName}`;
                input.id = `progress_quests-${index}-${fieldName}`;
            });
        });
    }

    function addToArray(fieldName) {
        const tbody = document.getElementById(`${fieldName}-tbody`);
        const currentCount = tbody.children.length;
        
        const newRow = document.createElement('tr');
        if (fieldName === "progress_quests") {
            newRow.className = 'sortable-row';
        }

        if (fieldName === "external_modifiers") {
            const typeChoices = ["nation", "character"].map(choice => [choice, choice]);
            
            const typeOptions = typeChoices.map(([value, label]) => 
                `<option value="${value}">${label}</option>`
            ).join('');
            
            newRow.innerHTML = `
                <td>
                    <select class="form-control" name="${fieldName}-${currentCount}-type" id="${fieldName}-${currentCount}-type">
                        ${typeOptions}
                    </select>
                </td>
                <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-modifier" id="${fieldName}-${currentCount}-modifier" required></td>
                <td><input type="float" class="form-control" name="${fieldName}-${currentCount}-value" id="${fieldName}-${currentCount}-value" required></td>
                <td><button type="button" class="btn btn-danger" onclick="removeFromArray(this, '${fieldName}')">Remove</button></td>
            `;
        } else if (fieldName === "modifiers") {
            newRow.innerHTML = `
                <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-field" id="${fieldName}-${currentCount}-field" required></td>
                <td><input type="float" class="form-control" name="${fieldName}-${currentCount}-value" id="${fieldName}-${currentCount}-value" required></td>
                <td><input type="float" class="form-control" name="${fieldName}-${currentCount}-duration" id="${fieldName}-${currentCount}-duration" value="0" required></td>
                <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-source" id="${fieldName}-${currentCount}-source" required></td>
                <td><button type="button" class="btn btn-danger" onclick="removeFromArray(this, '${fieldName}')">Remove</button></td>
            `;
        } else if (fieldName === "progress_quests") {
            newRow.innerHTML = `
                <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-quest_name" id="${fieldName}-${currentCount}-name" required></td>
                <td>
                    <select class="form-control" name="${fieldName}-${currentCount}-slot" id="${fieldName}-${currentCount}-slot">
                        <option value="no_slot">No Slot</option>
                        <option value="1_progress_slot">1 Progress Slot</option>
                        <option value="tier_1_spell_slot">Tier 1 Spell Slot</option>
                        <option value="tier_2_spell_slot">Tier 2 Spell Slot</option>
                        <option value="tier_3_spell_slot">Tier 3 Spell Slot</option>
                    </select>
                </td>
                <td><input type="number" class="form-control" name="${fieldName}-${currentCount}-bonus_progress_per_tick" id="${fieldName}-${currentCount}-bonus_progress_per_tick" value="0" required></td>
                <td><input type="number" class="form-control" name="${fieldName}-${currentCount}-current_progress" id="${fieldName}-${currentCount}-current_progress" value="0" required></td>
                <td><input type="number" class="form-control" name="${fieldName}-${currentCount}-required_progress" id="${fieldName}-${currentCount}-required_progress" value="0" required></td>
                <td><input type="text" class="form-control" name="${fieldName}-${currentCount}-link" id="${fieldName}-${currentCount}-link" required></td>
                <td>
                    <span class="drag-handle">⋮⋮</span>
                    <button type="button" class="btn btn-danger" onclick="removeFromArray(this, '${fieldName}')">Remove</button>
                </td>
            `;
        }

        tbody.appendChild(newRow);
    }

    function removeFromArray(button, categoryName) {
        const row = button.closest('tr');
        row.remove();
        
        if (categoryName === 'progress_quests') {
            reindexProgressQuests();
        } else {
            // Reindex remaining modifiers
            const tbody = document.getElementById(`${categoryName}-tbody`);
            Array.from(tbody.children).forEach((row, index) => {
                row.querySelectorAll('select, input').forEach(input => {
                    const fieldName = input.name.split('-').pop();
                    input.name = `${categoryName}-${index}-${fieldName}`;
                    input.id = `${categoryName}-${index}-${fieldName}`;
                });
            });
        }
    }
</script>

{% endblock %}
