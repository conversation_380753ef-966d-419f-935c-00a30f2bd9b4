{% extends "layout.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
	{% if g.user is not none %}
		<a href="{{ request.path }}/edit">Edit</a>
	{% endif %}
	<div class="action-links">
		<a href="{{ request.path }}/changes/pending">View Pending Changes</a>
		<a href="{{ request.path }}/changes/archived">View Archived Changes</a>
	</div>
	<h1>{{ title }}:</h1>
	<div class="table-wrapper">
		<table class="info-table">
			<thead>
				<tr>
					<th>
						{% if "name" in schema.properties %}
							Name
						{% else %}
							ID
						{% endif %}
					</th>
					{% for preview_category in schema.preview %}
						<th>{{ schema.properties[preview_category]["label"] }}</th>
					{% endfor %}
				</tr>
			</thead>
			<tbody>
				{% for item in items %}
					<tr>
						{% if item.name %}
							{# If there's a name, use it in the URL #}
							<td><a href="{{ request.path }}/item/{{ item.name }}">{{ item.name }}</a></td>
						{% else %}
							{# Otherwise, use the _id #}
							<td><a href="{{ request.path }}/item/{{ item._id|string }}">{{ item._id }}</a></td>
						{% endif %}
						{% for preview_category in schema.preview %}
							{% if preview_category in preview_references %}
								{% set item_value = item[preview_category]|string %}
								{% if preview_references.get(preview_category, {}).get(item_value, {}).get('link') %}
									<td><a href="{{ preview_references.get(preview_category, {}).get(item_value, {}).get('link', '') }}">{{ preview_references.get(preview_category, {}).get(item_value, {}).get('name', '') }}</td>
								{% elif item[preview_category] %}
									<td>{{ item[preview_category] }}</td>
								{% else %}
									<td>None</td>
								{% endif %}
							{% else %}
								<td>{{ item[preview_category] }}</td>
							{% endif %}
						{% endfor %}
					</tr>
				{% endfor %}
			</tbody>
		</table>
	</div>
</div>
{% endblock %}
