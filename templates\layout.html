<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Path of Ages{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <script src="{{ url_for('static', filename='js/expandable-sections.js') }}"></script>
</head>
<body>
	<header>
		<nav class="navbar">
			<div class="nav-left">
				<ul class="nav-list">
					<li class="nav-item"><a href="/">Home</a></li>
					<li class="nav-item"><a href="/map">Map</a></li>

					<li class="nav-item dropdown">
						<a href="#" class="dropbtn">National Entities</a>
						<ul class="dropdown-content">
							<li><a href="/factions">Factions</a></li>
							<li><a href="/regions">Regions</a></li>
							<li><a href="/mercenaries">Mercenaries</a></li>
							<li><a href="/merchants">Merchants</a></li>
							<li><a href="/nations">Nations</a></li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a href="#" class="dropbtn">Demographics</a>
						<ul class="dropdown-content">
							<li><a href="/races">Races</a></li>
							<li><a href="/cultures">Cultures</a></li>
							<li><a href="/religions">Religions</a></li>
							<li><a href="/pops">Pops</a></li>
						</ul>
					</li>
					
					<li class="nav-item dropdown">
						<a href="#" class="dropbtn">Misc</a>
						<ul class="dropdown-content">
							<li><a href="/artifacts">Artifacts</a></li>
							<li><a href="/characters">Characters</a></li>
							<li><a href="/diplo_relations">Diplomatic Relations</a></li>
							<li><a href="/markets">Markets</a></li>
							<li><a href="/market_links">Market Links</a></li>
							<li><a href="/spells">Spells</a></li>
							<li><a href="/units">Units</a></li>
							<li><a href="/wars">Wars</a></li>
							<li><a href="/wonders">Wonders</a></li>
						</ul>
					</li>
				</ul>
			</div>
			<div class="nav-right">
				<ul class="nav-list">
					{% if g.user %}
						{% if g.user.is_admin %}
							<li class="nav-item dropdown">
								<a href="#" class="dropbtn">Admin Tools</a>
								<ul class="dropdown-content">
									<li><a href="/changes">Changes</a></li>
									<li><a href="/database_management">Database Management</a></li>
									<li><a href="/demographics_overview">Demographics Overview</a></li>
									<li><a href="/elected_candidates_generator">Elected Candidates Generator</a></li>
									<li><a href="/global_modifiers/item/global_modifiers">Global Modifiers</a></li>
									<li><a href="/karma_helper">Karma Helper</a></li>
									<li><a href="/players">Players</a></li>
									<li><a href="/pop_growth_helper">Pop Growth Helper</a></li>
									<li><a href="/tick_helper">Tick Helper</a></li>
									<li><a href="/tick_summaries">Tick Summaries</a></li>
									<li><a href="/temperament_overview">Temperament Overview</a></li>
								</ul>
							</li>
						{% endif %}
						
						<li class="nav-item dropdown">
							{% if user_entities %}
								<a href="#" class="dropbtn">Quick Links</a>
								<ul class="dropdown-content">
									{% if user_entities.characters %}
										{% for character in user_entities.characters %}
											<li><a href="/characters/item/{{ character.name }}">{{ character.name }}</a></li>
										{% endfor %}
									{% endif %}
									
									{% if user_entities.nations %}
										{% for nation in user_entities.nations %}
											<li><a href="/nations/item/{{ nation.name }}">{{ nation.name }}</a></li>
										{% endfor %}
									{% endif %}
									
									{% if user_entities.mercenaries %}
										{% for mercenary in user_entities.mercenaries %}
											<li><a href="/mercenaries/item/{{ mercenary.name }}">{{ mercenary.name }}</a></li>
										{% endfor %}
									{% endif %}

									{% if user_entities.merchants %}
										{% for merchant in user_entities.merchants %}
											<li><a href="/merchants/item/{{ merchant.name }}">{{ merchant.name }}</a></li>
										{% endfor %}
									{% endif %}

									{% if user_entities.factions %}
										{% for faction in user_entities.factions %}
											<li><a href="/factions/item/{{ faction.name }}">{{ faction.name }}</a></li>
										{% endfor %}
									{% endif %}
								</ul>
							{% endif %}
						</li>

						<a href="/logout">Logout</a>
						<a href="/players/item/{{ g.user.name }}">
							<img src="{{ g.user.avatar_url }}" alt="User Avatar">
						</a>
					{% else %}
						<a href="/login">Login</a>
					{% endif %}
				</ul>
			</div>
		</nav>
	</header>
    <main>
		{% with messages = get_flashed_messages() %}
		  {% if messages %}
			<ul class=flashes>
			{% for message in messages %}
			  <li>{{ message }}</li>
			{% endfor %}
			</ul>
		  {% endif %}
		{% endwith %}
        {% block content %}There should be something here...{% endblock %}
    </main>
</body>
</html>
