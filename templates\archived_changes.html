{% extends "layout.html" %}
{% from "macros/pagination.html" import render_pagination, render_changes_table %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="nav-tabs">
        <a href="/changes/pending">Pending Changes</a>
        <a href="/changes/archived" class="active">Archived Changes</a>
    </div>
    
    <h1>Archived Changes ({{ total_count }} total)</h1>
    
    <!-- Top Pagination -->
    {% if total_pages > 1 %}
        {{ render_pagination(current_page, total_pages, "/changes/archived") }}
    {% endif %}
    
    <div class="table-wrapper">
        {{ render_changes_table(changes, preview_references, target_schemas, view_access_level) }}
        
        <!-- Bottom Pagination -->
        {% if total_pages > 1 %}
            {{ render_pagination(current_page, total_pages, "/changes/archived") }}
        {% endif %}
    </div>
</div>
{% endblock %}

