{"$jsonSchema": {"bsonType": "object", "required": ["name"], "preview": ["ruling_nation_org", "player", "region"], "laws": ["character_type", "health_status"], "external_calculation_requirements": {"race": ["positive_trait", "negative_trait"], "religion": ["external_modifiers"], "artifacts": ["external_modifiers"], "region": ["external_modifiers"]}, "properties": {"name": {"bsonType": "string", "label": "Name", "description": "Name of the Character"}, "player": {"bsonType": "linked_object", "label": "Player", "description": "Who is playing the Character", "noneResult": "AI", "collections": ["players"]}, "creator": {"bsonType": "linked_object", "label": "Creator", "description": "Who made the Character", "noneResult": "Admin", "collections": ["players"], "static": true}, "ruling_nation_org": {"bsonType": "linked_object", "label": "Ruling Nation/Organization", "description": "The nation or organization the character is currently ruling", "noneResult": "None", "collections": ["nations", "merchants", "mercenaries", "factions"]}, "region": {"bsonType": "linked_object", "label": "Region", "description": "The region the character is currently in", "noneResult": "Unknown", "collections": ["regions"]}, "race": {"bsonType": "linked_object", "label": "Race", "description": "The race of the character", "noneResult": "Unknown", "collections": ["races"]}, "culture": {"bsonType": "linked_object", "label": "Culture", "description": "The culture of the character", "noneResult": "Unknown", "collections": ["cultures"]}, "religion": {"bsonType": "linked_object", "label": "Religion", "description": "The religion of the character", "noneResult": "Unknown", "collections": ["religions"]}, "character_type": {"bsonType": "enum", "label": "Character Type", "description": "The type of the character", "enum": ["Steward", "Religious Leader", "Conqueror", "Populist", "Archmage", "Mage", "Warrior", "General", "Merchant", "Rogue"], "static": true, "laws": {"Steward": {"rulership": 2, "cunning": 1, "charisma": 0, "prowess": -1, "magic": 0, "strategy": 0}, "Religious Leader": {"rulership": 1, "cunning": 1, "charisma": 1, "prowess": -1, "magic": 1, "strategy": -1}, "Conqueror": {"rulership": 0, "cunning": 0, "charisma": -1, "prowess": 2, "magic": 0, "strategy": 1}, "Populist": {"rulership": -1, "cunning": 2, "charisma": 2, "prowess": -1, "magic": 0, "strategy": -1}, "Archmage": {"rulership": 0, "cunning": 2, "charisma": -1, "prowess": -2, "magic": 2, "strategy": 0}, "Mage": {"rulership": 0, "cunning": 2, "charisma": -1, "prowess": 0, "magic": 2, "strategy": -1}, "Warrior": {"rulership": -1, "cunning": 1, "charisma": -1, "prowess": 2, "magic": 0, "strategy": 1}, "General": {"rulership": 1, "cunning": 0, "charisma": -1, "prowess": 0, "magic": -1, "strategy": 3}, "Merchant": {"rulership": 1, "cunning": 1, "charisma": 2, "prowess": -1, "magic": 0, "strategy": -1}, "Rogue": {"rulership": -1, "cunning": 2, "charisma": 1, "prowess": 1, "magic": 0, "strategy": -1}}}, "character_subtype": {"bsonType": "enum", "label": "Character Subtype", "description": "The subtype of the character", "enum": ["None", "Artificer", "Diplomat", "Duelist", "Orator", "<PERSON>", "Proselytizer", "Quartermaster", "Scholar", "Statesman", "<PERSON><PERSON>"], "static": true}, "strengths": {"bsonType": "array", "label": "Strengths", "description": "The strengths of the character", "static": true, "items": {"bsonType": "string"}}, "weaknesses": {"bsonType": "array", "label": "Weaknesses", "description": "The weaknesses of the character", "static": true, "items": {"bsonType": "string"}}, "artifacts": {"bsonType": "array", "label": "Artifacts", "description": "The list of artifacts that currently belong to this character", "collections": ["artifacts"], "queryTargetAttribute": "owner", "preview": ["rarity", "equipped"], "items": {"bsonType": "linked_object"}}, "title_slots": {"bsonType": "number", "label": "Title Slots", "description": "The number of title slots the character has available", "calculated": true, "base_value": 3}, "titles": {"bsonType": "array", "label": "Titles", "description": "The titles the character currently holds", "static": true, "max_length": "title_slots", "items": {"bsonType": "json_district_enum", "json_data": "titles"}}, "age": {"bsonType": "number", "label": "Age", "description": "How old the Character is", "default": 1}, "elderly_age": {"bsonType": "number", "label": "Elderly Age", "description": "The age at which the Character is considered elderly", "calculated": true, "base_value": 3}, "age_status": {"bsonType": "enum", "label": "Age Status", "description": "The current age status of the Character", "enum": ["Child", "Adult", "Elderly"], "calculated": true}, "health_status": {"bsonType": "enum", "label": "Health Status", "description": "The current health status of the Character", "enum": ["Healthy", "Injured", "<PERSON><PERSON>", "Bedridden", "Dead"], "laws": {"Healthy": {}, "Injured": {"death_chance": 0.05, "stats": -1}, "Maimed": {"death_chance": 0.1, "stats": -2}, "Bedridden": {"death_chance": 0.15, "stats": -3}, "Dead": {"death_chance_maximum": 0}}}, "death_chance": {"bsonType": "number", "label": "Death Chance", "description": "The percentage chance of the Character dying each tick", "calculated": true, "format": "percentage"}, "heal_chance": {"bsonType": "number", "label": "Heal <PERSON>", "description": "The percentage chance of the Character healing each tick", "calculated": true, "base_value": 0.05, "heal_chance_per_prowess": 0.05, "format": "percentage"}, "stat_gain_chance": {"bsonType": "number", "label": "Stat Gain Chance", "description": "The percentage chance of the Character gaining a stat point each tick", "calculated": true, "format": "percentage"}, "rulership_cap": {"bsonType": "number", "label": "Rulership Cap", "description": "The maximum Rulership stat of the character", "calculated": true, "hidden": true, "base_value": 6}, "cunning_cap": {"bsonType": "number", "label": "Cunning Cap", "description": "The maximum Cunning stat of the character", "calculated": true, "hidden": true, "base_value": 6}, "charisma_cap": {"bsonType": "number", "label": "Charisma Cap", "description": "The maximum Charisma stat of the character", "calculated": true, "hidden": true, "base_value": 6}, "prowess_cap": {"bsonType": "number", "label": "Prowess Cap", "description": "The maximum Prowess stat of the character", "calculated": true, "hidden": true, "base_value": 6}, "magic_cap": {"bsonType": "number", "label": "Magic Cap", "description": "The maximum Magic stat of the character", "calculated": true, "hidden": true, "base_value": 6}, "strategy_cap": {"bsonType": "number", "label": "Strategy Cap", "description": "The maximum Strategy stat of the character", "calculated": true, "hidden": true, "base_value": 6}, "rulership": {"bsonType": "number", "label": "Rulership", "description": "The Rulership stat of the character", "calculated": true, "max": "rulership_cap"}, "cunning": {"bsonType": "number", "label": "<PERSON><PERSON><PERSON>", "description": "The Cunning stat of the character", "calculated": true, "max": "cunning_cap"}, "charisma": {"bsonType": "number", "label": "Charisma", "description": "The Charisma stat of the character", "calculated": true, "max": "charisma_cap"}, "prowess": {"bsonType": "number", "label": "Prowess", "description": "The Prowess stat of the character", "calculated": true, "max": "prowess_cap"}, "magic": {"bsonType": "number", "label": "Magic", "description": "The Magic stat of the character", "calculated": true, "max": "magic_cap"}, "strategy": {"bsonType": "number", "label": "Strategy", "description": "The Strategy stat of the character", "calculated": true, "max": "strategy_cap"}, "positive_quirk": {"bsonType": "enum", "label": "Positive Quirk", "description": "The positive quirk of the character", "static": true, "enum": ["None", "Articulate", "Brave", "Determined", "Compassionate", "Generous", "<PERSON>mble", "Imaginative", "Just", "Observant", "Optimistic", "Patient", "<PERSON>"]}, "negative_quirk": {"bsonType": "enum", "label": "Negative Quirk", "description": "The negative quirk of the character", "static": true, "enum": ["None", "Aggressive", "Apathetic", "Arrogant", "<PERSON><PERSON>", "Careless", "<PERSON><PERSON>ly", "Deceitful", "Empathetic", "Fickle", "Greedy", "Lazy", "Pessimistic", "Repulsive"]}, "magic_points": {"bsonType": "number", "label": "Magic Points", "description": "The amount of magic points the Character currently has in storage"}, "magic_point_income": {"bsonType": "number", "label": "Magic Point Production", "description": "The amount of magic points the Character produces each tick", "calculated": true}, "magic_point_capacity": {"bsonType": "number", "label": "Magic Point Capacity", "description": "The amount of magic points the Character can hold", "magic_point_capacity_per_magic": 3, "calculated": true}, "progress_quests": {"bsonType": "array", "label": "Progress Quests", "description": "The ongoing quests the merchant company is currently working on", "items": {"bsonType": "object", "properties": {"quest_name": {"bsonType": "string", "label": "Quest Name", "description": "The name of the quest"}, "progress_per_tick": {"bsonType": "number", "label": "Progress Per Tick", "description": "The amount of progress the merchant company makes on the quest each tick"}, "current_progress": {"bsonType": "number", "label": "Progress", "description": "The amount of progress the merchant company has made on the quest"}, "required_progress": {"bsonType": "number", "label": "Required Progress", "description": "The amount of progress required to complete the quest"}, "link": {"bsonType": "string", "label": "Link", "description": "The link to the quest"}}}}, "modifiers": {"bsonType": "array", "label": "Ongoing Modifiers", "description": "The ongoing modifiers currently affecting this characterr", "items": {"bsonType": "object", "properties": {"field": {"bsonType": "string", "label": "Modifier Field", "description": "The calculated field the modifier affects"}, "value": {"bsonType": "number", "label": "Modifier Value", "description": "The value of the modifier"}, "duration": {"bsonType": "number", "label": "Modifier Duration", "description": "The duration of the modifier"}, "source": {"bsonType": "string", "label": "Modifier Source", "description": "The source of the modifier"}}}}}}}