{"$jsonSchema": {"bsonType": "object", "required": ["name"], "properties": {"name": {"bsonType": "string", "label": "Name", "description": "The name of the region"}, "nations": {"bsonType": "array", "label": "Nations", "description": "Nations currently located in this region", "collections": ["nations"], "queryTargetAttribute": "region", "items": {"bsonType": "linked_object"}}, "merchants": {"bsonType": "array", "label": "Merchants", "description": "Merchants currently located in this region", "collections": ["merchants"], "queryTargetAttribute": "region", "items": {"bsonType": "linked_object"}}, "mercenaries": {"bsonType": "array", "label": "Mercenaries", "description": "Mercenaries currently located in this region", "collections": ["mercenaries"], "queryTargetAttribute": "region", "items": {"bsonType": "linked_object"}}, "characters": {"bsonType": "array", "label": "Characters", "description": "Characters currently located in this region", "collections": ["characters"], "queryTargetAttribute": "region", "items": {"bsonType": "linked_object"}}, "wars": {"bsonType": "array", "label": "Wars", "description": "Wars currently being fought in this region", "collections": ["wars"], "queryTargetAttribute": "region", "items": {"bsonType": "linked_object"}}, "external_modifiers": {"bsonType": "array", "label": "Mechanical Modifiers", "description": "The mechanical modifiers that affect the owner", "hidden": true, "items": {"bsonType": "object", "properties": {"type": {"bsonType": "enum", "label": "Type", "description": "The type of entity the modifier affects", "enum": ["nation", "character"]}, "modifier": {"bsonType": "string", "label": "Modifier", "description": "The modifier"}, "value": {"bsonType": "number", "label": "Modifier Value", "description": "The value of the modifier"}}}}}}}