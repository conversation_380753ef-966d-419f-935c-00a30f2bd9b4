{% extends "layout.html" %}

{% block content %}
<div class="container mt-4">
    <h1>Tick Summaries</h1>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">Available Tick Summaries</h3>
        </div>
        <div class="card-body">
            {% if summaries %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Size</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for summary in summaries %}
                                <tr>
                                    <td>{{ summary.date }}</td>
                                    <td>{{ (summary.size / 1024)|round(1) }} KB</td>
                                    <td>
                                        <a href="{{ url_for('admin_tool_routes.download_tick_summary', filename=summary.filename) }}" class="btn btn-sm btn-primary">View</a>
                                        <a href="{{ url_for('admin_tool_routes.download_tick_summary', filename=summary.filename, download='true') }}" class="btn btn-sm btn-secondary">Download</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    No tick summaries found.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}