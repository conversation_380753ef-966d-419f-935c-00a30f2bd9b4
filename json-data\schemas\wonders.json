{"$jsonSchema": {"bsonType": "object", "required": ["name", "effect_description"], "preview": ["era_created", "owner_nation"], "properties": {"name": {"bsonType": "string", "label": "Name", "description": "The name of the wonder"}, "era_created": {"bsonType": "enum", "label": "Era Created", "description": "The era the wonder was created in", "enum": ["Ancient", "Classical", "Medieval", "Industrial", "Modern", "Crisis"], "default": "Classical"}, "legacy_status": {"bsonType": "boolean", "label": "Legacy Status", "description": "Whether the wonder has a legacy bonus"}, "node": {"bsonType": "json_resource_enum", "label": "Node", "description": "The node under the wonder"}, "effect_description": {"bsonType": "string", "label": "Effect Description", "description": "The description of the wonder's mechanical effects", "long_text": true}, "mechanical_effects": {"bsonType": "array", "label": "Mechanical Effects", "description": "The wonder's mechanical effects", "hidden": true}, "owner_nation": {"bsonType": "linked_object", "label": "Owner", "description": "The nation that currently controls the wonder", "noneResult": "None", "collections": ["nations"]}, "creator_nation": {"bsonType": "linked_object", "label": "Creator", "description": "The nation that originally created the wonder", "noneResult": "Unknown", "collections": ["nations"]}, "external_modifiers": {"bsonType": "array", "label": "Mechanical Modifiers", "description": "The mechanical modifiers that affect the owner", "hidden": true, "items": {"bsonType": "object", "properties": {"type": {"bsonType": "enum", "label": "Type", "description": "The type of entity the modifier affects", "enum": ["nation", "character"]}, "modifier": {"bsonType": "string", "label": "Modifier", "description": "The modifier"}, "value": {"bsonType": "number", "label": "Modifier Value", "description": "The value of the modifier"}}}}}}}