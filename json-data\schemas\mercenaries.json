{"$jsonSchema": {"bsonType": "object", "required": ["name", "reknown"], "laws": ["renown", "size"], "preview": ["region", "size", "patron"], "sort": "region", "properties": {"name": {"bsonType": "string", "label": "Name", "description": "The name of the merchant company"}, "leaders": {"bsonType": "array", "label": "Leaders", "description": "The list of leaders of the merchant company", "collections": ["characters"], "queryTargetAttribute": "ruling_nation_org", "items": {"bsonType": "linked_object"}, "preview": ["name", "character_type"]}, "region": {"bsonType": "linked_object", "label": "Region", "description": "The region the mercenary company is currently stationed in", "collections": ["regions"]}, "patron": {"bsonType": "linked_object", "label": "<PERSON><PERSON>", "description": "The nation that currently employs the mercenary company", "collections": ["nations"]}, "land_doctrine": {"bsonType": "enum", "label": "Land Doctrine", "description": "The nation's general approach to land based warfare", "enum": ["Standard", "Nomadic", "Shock", "Volley", "Safeguard", "Militia", "Ambush"], "laws": {"Standard": {}, "Nomadic": {"land_unit_speed": 1}, "Shock": {"first_session_land_attack": 2, "after_first_session_land_defense": -4}, "Volley": {"land_unit_speed_mult": 0.5, "land_unit_range_when_stationary": 1}, "Safeguard": {"land_defense_against_ranged": 2, "defensive_war_land_defense": 1, "offensive_war_land_attack": -4}, "Militia": {}, "Ambush": {"land_attack_vs_damaged_units": 2, "land_non_ruler_unit_speed": 1, "land_cavalry_unit_speed": -1, "land_unit_hp_mult": 0.75}}}, "naval_doctrine": {"bsonType": "enum", "label": "Naval Doctrine", "description": "The nation's general approach to aquatic warfare", "enum": ["Standard", "Skirmish", "Fleet", "Transport"], "laws": {"Standard": {}, "Skirmish": {}, "Fleet": {}, "Transport": {}}}, "size": {"bsonType": "enum", "label": "Size", "description": "The size of the mercenary company", "enum": ["Small", "Medium", "Large", "Massive"], "laws": {"Small": {"budget": 450, "max_units": 2}, "Medium": {"budget": 750, "max_units": 3}, "Large": {"budget": 1050, "max_units": 4}, "Massive": {"budget": 1500, "max_units": 5}}}, "renown": {"bsonType": "enum", "label": "Renown", "description": "The mercenary company's reputation", "enum": ["Scoundrels", "Unscrupulous", "Standard", "Honorable"], "default": "Standard", "laws": {"Scoundrels": {"attack": 2, "hiring_cost_mult": -0.25}, "Unscrupulous": {"strength": 1, "hiring_cost_mult": -0.15}, "Standard": {"strength": 1, "hiring_cost_mult": -0.1}, "Honorable": {"defense": 2}}}, "upkeep": {"bsonType": "number", "label": "Upkeep", "description": "The amount of money the mercenary company has to pay each session in upkeep", "calculated": true}, "treasury": {"bsonType": "number", "label": "Treasury", "description": "The amount of money the mercenary company has in its coffers", "view_access_level": 5}, "land_budget": {"bsonType": "number", "label": "Land Budget", "description": "The amount of money the mercenary company has to recruit land units with", "calculated": true}, "land_budget_spent": {"bsonType": "number", "label": "Land Budget Spent", "description": "The amount of money the mercenary company has spent to recruit land units", "calculated": true}, "naval_budget": {"bsonType": "number", "label": "Naval Budget", "description": "The amount of money the mercenary company has to recruit naval units with", "calculated": true}, "naval_budget_spent": {"bsonType": "number", "label": "Naval Budget Spent", "description": "The amount of money the mercenary company has spent to recruit naval units", "calculated": true}, "hiring_cost": {"bsonType": "number", "label": "Hiring Cost", "description": "The minimum amount of money the mercenary company charges to hire", "calculated": true}, "max_units": {"bsonType": "number", "label": "Max Units", "description": "The maximum number of units the mercenary company can field", "calculated": true}, "districts": {"bsonType": "array", "label": "Districts", "description": "The districts the mercenary company owns", "max_length": 3, "items": {"bsonType": "json_district_enum", "json_data": "mercenary_districts"}}, "land_units": {"bsonType": "array", "label": "Land Units", "description": "The units the mercenary company owns", "max_length": "max_units", "items": {"bsonType": "json_unit_enum", "json_data": ["ancient_magical_land_units", "ancient_mundane_land_units", "classical_magical_land_units", "classical_mundane_land_units", "ancient_unique_land_units", "classical_unique_land_units"]}}, "naval_units": {"bsonType": "array", "label": "Naval Units", "description": "The units the mercenary company owns", "max_length": "max_units", "items": {"bsonType": "json_unit_enum", "json_data": ["ancient_mundane_naval_units", "classical_magical_naval_units", "classical_mundane_naval_units"]}}, "progress_quests": {"bsonType": "array", "label": "Progress Quests", "description": "The ongoing quests the mercenary company is currently working on", "view_access_level": 5, "items": {"bsonType": "object", "properties": {"quest_name": {"bsonType": "string", "label": "Quest Name", "description": "The name of the quest"}, "progress_per_tick": {"bsonType": "number", "label": "Progress Per Tick", "description": "The amount of progress the mercenary company makes on the quest each tick"}, "current_progress": {"bsonType": "number", "label": "Progress", "description": "The amount of progress the mercenary company has made on the quest"}, "required_progress": {"bsonType": "number", "label": "Required Progress", "description": "The amount of progress required to complete the quest"}, "link": {"bsonType": "string", "label": "Link", "description": "The link to the quest"}}}}}}}