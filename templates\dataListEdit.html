{% extends "layout.html" %}

{% block title %}Edit {{ title }}{% endblock %}

{% block content %}
<div class="container">
	<a href="{{ request.path.replace('/edit', '') }}">Back to View</a></br>
	<a href="{{ request.path.replace('/edit', '/new') }}">New</a>
	<h1>{{ title }}:</h1>
	<table class="info-table">
		{% for item in items %}
			<tr>
				{% if item.name %}
					<th><a href="{{ request.path }}/{{ item.name }}">{{ item.name }}</a></th>
	            {% else %}
					<th><a href="{{ request.path }}/{{ item._id|string }}">{{ item._id }}</a></th>
	            {% endif %}
	            <td>
					{% if g.user.is_admin %}
						{% if item.name %}
							{% set default_action = request.path.replace('edit', 'delete') ~ '/' ~ item.name ~ '/save' %}
						{% else %}
							{% set default_action = request.path.replace('edit', 'delete') ~ '/' ~ item._id ~ '/save' %}
						{% endif %}
					{% else %}
						{% if item.name %}
							{% set default_action = request.path.replace('edit', 'delete') ~ '/' ~ item.name ~ '/request' %}
						{% else %}
							{% set default_action = request.path.replace('edit', 'delete') ~ '/' ~ item._id ~ '/request' %}
						{% endif %}
					{% endif %}
	            	<form method="POST" action="{{ default_action }}">
	            		<label for="reason">Reason</label>
						<input type="text" id="reason" name="reason" value="" required >
						{% if g.user.is_admin %}
							{% if item.name %}
								<button type="submit" formaction="{{ request.path.replace('edit', 'delete') }}/{{ item.name }}/save">Immediate Delete</button>
							{% else %}
								<button type="submit" formaction="{{ request.path.replace('edit', 'delete') }}/{{ item._id }}/save">Immediate Delete</button>
							{% endif %}
						{% endif %}
						{% if item.name %}
							<button type="submit" formaction="{{ request.path.replace('edit', 'delete') }}/{{ item.name }}/request">Request Delete</button>
						{% else %}
							<button type="submit" formaction="{{ request.path.replace('edit', 'delete') }}/{{ item._id }}/request">Request Delete</button>
						{% endif %}
					</form>
				</td>
            </tr>
		{% endfor %}
	</table>
</div>
{% endblock %}
