{"$jsonSchema": {"bsonType": "object", "required": ["name", "node_type"], "properties": {"name": {"bsonType": "string", "label": "Name", "description": "Name of the node"}, "node_type": {"bsonType": "string", "label": "Node Type", "description": "Type of node", "enum": ["resource", "strategic", "trade", "magical", "cultural", "defensive"]}, "description": {"bsonType": "string", "label": "Description", "description": "Description of the node and its effects"}, "resource_type": {"bsonType": ["string", "null"], "label": "Resource Type", "description": "Type of resource this node provides (if applicable)"}, "resource_amount": {"bsonType": ["int", "null"], "label": "Resource Amount", "description": "Amount of resource this node provides per turn"}, "modifiers": {"bsonType": "object", "label": "Modifiers", "description": "Stat modifiers this node provides to the controlling nation"}, "requirements": {"bsonType": "object", "label": "Requirements", "description": "Requirements to utilize this node"}, "image_path": {"bsonType": ["string", "null"], "label": "Image Path", "description": "Path to the node's icon image"}}}}