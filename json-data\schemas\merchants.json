{"$jsonSchema": {"bsonType": "object", "required": ["name", "reputation"], "laws": ["reputation"], "preview": ["location"], "sort": "location", "properties": {"name": {"bsonType": "string", "label": "Name", "description": "The name of the merchant company"}, "leaders": {"bsonType": "array", "label": "Leaders", "description": "The list of leaders of the merchant company", "collections": ["characters"], "queryTargetAttribute": "ruling_nation_org", "items": {"bsonType": "linked_object"}}, "reputation": {"bsonType": "enum", "label": "Reputation", "description": "The merchant company's reputation", "enum": ["Poor", "Acceptable", "Great", "Flawless"], "laws": {"Poor": {"hostile_encounter_chance": 0.5, "income": 75}, "Acceptable": {"hostile_encounter_chance": 0.35, "income": 150}, "Great": {"hostile_encounter_chance": 0.3, "income": 225}, "Flawless": {"hostile_encounter_chance": 0.2, "income": 300}}}, "hostile_encounter_chance": {"bsonType": "number", "label": "Hostile Encounter Chance", "description": "The percentage chance of having a hostile encounter when the merchant company moves", "calculated": true, "format": "percentage"}, "location": {"bsonType": "linked_object", "label": "Current Location", "description": "The nation the merchant company is currently stationed in", "collections": ["nations"]}, "income": {"bsonType": "number", "label": "Income", "description": "The amount of money the merchant company makes at the start of each session", "calculated": true}, "treasury": {"bsonType": "number", "label": "Treasury", "description": "The amount of money the merchant company has in its coffers"}, "trade_distance": {"bsonType": "number", "label": "Trade Distance", "description": "The distance at which the merchant company can trade with other nations or merchant companies", "calculated": true, "base_value": 15}, "import_slots": {"bsonType": "number", "label": "Import Slots", "description": "The amount of resources the merchant company can import each session", "calculated": true, "base_value": 9}, "export_slots": {"bsonType": "number", "label": "Export Slots", "description": "The amount of resources the merchant company can export each session", "calculated": true, "base_value": 9}, "resource_storage": {"bsonType": "object", "label": "Resource Storage", "description": "The resource reserves of the merchant company"}, "resource_production": {"bsonType": "object", "label": "Resource Production", "description": "The amount of each resource the merchant company makes at the start of each session", "calculated": true}, "production_district_1": {"bsonType": "json_district_enum", "label": "Production District 1", "description": "The first production district of the merchant company", "json_data": "merchant_production_districts"}, "production_district_2": {"bsonType": "json_district_enum", "label": "Production District 2", "description": "The second production district of the merchant company", "json_data": "merchant_production_districts"}, "production_district_3": {"bsonType": "json_district_enum", "label": "Production District 3", "description": "The second production district of the merchant company", "json_data": "merchant_production_districts"}, "specialty_district": {"bsonType": "json_district_enum", "label": "Specialty District", "description": "The specialty district of the merchant company", "json_data": "merchant_specialty_districts"}, "luxury_district": {"bsonType": "json_district_enum", "label": "Luxury District", "description": "The luxury district of the merchant company", "json_data": "merchant_luxury_districts"}, "progress_quests": {"bsonType": "array", "label": "Progress Quests", "description": "The ongoing quests the merchant company is currently working on", "items": {"bsonType": "object", "properties": {"quest_name": {"bsonType": "string", "label": "Quest Name", "description": "The name of the quest"}, "progress_per_tick": {"bsonType": "number", "label": "Progress Per Tick", "description": "The amount of progress the merchant company makes on the quest each tick"}, "current_progress": {"bsonType": "number", "label": "Progress", "description": "The amount of progress the merchant company has made on the quest"}, "required_progress": {"bsonType": "number", "label": "Required Progress", "description": "The amount of progress required to complete the quest"}, "link": {"bsonType": "string", "label": "Link", "description": "The link to the quest"}}}}}}}