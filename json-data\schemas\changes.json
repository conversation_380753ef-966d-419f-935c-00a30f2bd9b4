{"$jsonSchema": {"bsonType": "object", "required": ["target", "target_collection", "request_reason"], "preview": ["target_collection", "change_type", "status", "target", "last_modified_time", "time_requested", "time_implemented", "time_denied", "time_reverted", "requester", "approver", "denier", "reverter", "before_requested_data", "after_requested_data", "before_implemented_data", "after_implemented_data", "before_revert_data", "after_revert_data", "request_reason"], "properties": {"target_collection": {"bsonType": "string", "label": "Target Collection", "description": "The collection where the target is stored", "static": true}, "target": {"bsonType": "linked_object", "label": "Target", "description": "The object on which the changes are happening", "static": true}, "last_modified_time": {"bsonType": "date", "label": "Last Modified Time", "description": "The time the change was last modified", "static": true}, "time_requested": {"bsonType": "date", "label": "Time Requested", "description": "The time the change was requested", "static": true}, "time_implemented": {"bsonType": "date", "label": "Time Implemented", "description": "The time the change was implemented"}, "time_denied": {"bsonType": "date", "label": "Time Denied", "description": "The time the change was denied"}, "time_reverted": {"bsonType": "date", "label": "Time Reverted", "description": "The time the change was reverted"}, "requester": {"bsonType": "linked_object", "label": "Requester", "description": "The user that requested the change", "noneResult": "None", "collections": ["players"], "static": true}, "approver": {"bsonType": "linked_object", "label": "Approver", "description": "The user that approved the change", "noneResult": "None", "collections": ["players"]}, "denier": {"bsonType": "linked_object", "label": "Denier", "description": "The user that rejected the change", "noneResult": "None", "collections": ["players"]}, "reverter": {"bsonType": "linked_object", "label": "<PERSON><PERSON><PERSON>", "description": "The user that reverted the change", "noneResult": "None", "collections": ["players"]}, "change_type": {"bsonType": "enum", "label": "Change Type", "description": "The type of change", "enum": ["Update", "Add", "Remove"]}, "before_requested_data": {"bsonType": "object", "label": "Before Requested Data", "description": "The data before the change was requested", "static": true}, "after_requested_data": {"bsonType": "object", "label": "After Requested Data", "description": "What the data would have been if it was implemented immediately", "static": true}, "differential_data": {"bsonType": "object", "label": "Differential Data", "description": "The difference between the before requested and after requested data.  Only numbers are included", "static": true}, "before_implemented_data": {"bsonType": "object", "label": "Before Data", "description": "The data before the change was implemented.  If the change has not been implemented, this will be None"}, "after_implemented_data": {"bsonType": "object", "label": "After Data", "description": "The data after the change was implemented.  If the change has not been implemented, this will be None"}, "before_revert_data": {"bsonType": "object", "label": "Before Revert Data", "description": "The data before the change was reverted.  If the change has not been reverted, this will be None"}, "after_revert_data": {"bsonType": "object", "label": "After Revert Data", "description": "The data after the change was reverted.  If the change has not been reverted, this will be None"}, "request_reason": {"bsonType": "string", "label": "Reason", "description": "The text explanation and/or link to discord message explaining why the change should take place", "static": true}, "status": {"bsonType": "enum", "label": "Status", "description": "The status of the change", "enum": ["Pending", "Denied", "Approved", "Reverted"], "default": "Pending"}}}}