{"ancient_artifactory": {"display_name": "Ancient Artifactory", "type": "artifactory", "era": 2, "cost": {"stone": 4, "wood": 4, "magic": 3, "bronze": 1}, "modifiers": {"magic_consumption": 1, "artifact_owner_death_loss_chance": -0.05, "minimum_artifact_owner_death_loss_chance": 0.1}, "synergy_requirement": "magic", "synergy_node_active": true, "synergy_modifiers": {"artifact_owner_death_loss_chance": -0.05}, "count": 1}, "ancient_barrow": {"display_name": "Ancient Barrow", "type": "barrow", "era": 2, "cost": {"stone": 5, "magic": 4}, "modifiers": {"magic_consumption": 1, "nation_spell_cost": -1, "nation_spell_cost_minimum": 2}, "synergy_requirement": "magic", "synergy_node_active": true, "synergy_modifiers": {"ignore_magic_cost": true}, "count": 1}, "ancient_cache": {"display_name": "Ancient Cache", "type": "cache", "era": 2, "cost": {"stone": 4, "wood": 3, "bronze": 1}, "modifiers": {"wood_storage_capacity": 5, "stone_storage_capacity": 5, "mounts_storage_capacity": 5, "bronze_storage_capacity": 5}, "synergy_requirement": "stone", "synergy_node_active": true, "synergy_modifiers": {"wood_storage_capacity": 5, "stone_storage_capacity": 5, "mounts_storage_capacity": 5, "bronze_storage_capacity": 5}, "count": 1}, "ancient_center": {"display_name": "Ancient Center", "type": "center", "era": 2, "cost": {"stone": 4, "wood": 3, "food": 2}, "modifiers": {"wood_consumption": 1, "administration": 1, "bureaucrat_wood_upkeep": -1}, "synergy_requirement": "wood", "synergy_node_active": true, "synergy_modifiers": {"administration": 1}, "count": 1}, "ancient_church": {"display_name": "Ancient Church", "type": "church", "era": 2, "cost": {"stone": 2, "wood": 3, "bronze": 1}, "modifiers": {}, "synergy_requirement": "wood", "synergy_node_active": true, "synergy_modifiers": {}, "count": 1}, "ancient_dock": {"display_name": "Ancient Dock", "type": "dock", "era": 2, "cost": {"stone": 3, "wood": 5, "food": 2}, "modifiers": {"wood_consumption": 1, "food_production": 1}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_production": 1}, "count": 1}, "ancient_forge": {"display_name": "Ancient Forge", "type": "forge", "era": 2, "cost": {"stone": 6, "wood": 2, "bronze": 2}, "modifiers": {"stone_consumption": 1, "bronze_production": 1, "metallurgist_bronze_production": 1}, "synergy_requirement": "bronze", "synergy_node_active": true, "synergy_modifiers": {"mundane_unit_defense": 1}, "count": 1}, "ancient_fort": {"display_name": "Ancient Fort", "type": "fort", "era": 2, "cost": {"stone": 5, "wood": 3}, "alt_cost": {"stone": 5, "bronze": 1}, "modifiers": {"stone_consumption": 1}, "synergy_requirement": "stone", "synergy_node_active": true, "synergy_modifiers": {"ignore_stone_cost": true}, "count": 1}, "ancient_granary": {"display_name": "Ancient Granary", "type": "granary", "era": 2, "cost": {"stone": 2, "wood": 5}, "modifiers": {"food_storage_capacity": 5}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_storage_capacity": 5}, "count": 1}, "ancient_home": {"display_name": "Ancient Home", "type": "home", "era": 2, "cost": {"stone": 3, "wood": 5, "bronze": 2}, "modifiers": {"wood_consumption": 1, "effective_pop_capacity": 2}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"effective_pop_capacity": 1}, "count": 1}, "ancient_hospital": {"display_name": "Ancient Hospital", "type": "hospital", "era": 2, "cost": {"stone": 3, "wood": 4, "food": 2, "bronze": 1}, "modifiers": {"food_consumption": 1}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {}, "count": 1}, "ancient_farms": {"display_name": "Ancient Farms", "type": "farm", "era": 2, "cost": {"stone": 2, "wood": 6, "food": 4}, "modifiers": {"wood_consumption": 1, "food_production": 2}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_production": 1}, "count": 1}, "ancient_fishery": {"display_name": "Ancient Fishery", "type": "fishery", "era": 2, "cost": {"wood": 5, "food": 5}, "modifiers": {"wood_consumption": 1, "fisherman_food_production": 1, "food_production": 1}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_production": 1}, "count": 1}, "ancient_magic_site": {"display_name": "Ancient Magic Site", "type": "magic_site", "era": 2, "cost": {"magic": 4, "stone": 3}, "modifiers": {"stone_consumption": 1, "magic_production": 1}, "synergy_requirement": "magic", "synergy_node_active": true, "synergy_modifiers": {"magic_production": 1}, "count": 1}, "ancient_market": {"display_name": "Ancient Market", "type": "market", "era": 2, "cost": {"wood": 5, "stone": 2, "food": 3}, "modifiers": {"mounts_consumption": 1, "trade_distance": 3, "trade_slots": 1, "money_income": 100}, "synergy_requirement": "luxury", "synergy_node_active": false, "synergy_modifiers": {"money_income": 150}, "count": 1}, "ancient_mill": {"display_name": "Ancient Mill", "type": "mill", "era": 2, "cost": {"wood": 4, "stone": 2, "food": 6}, "modifiers": {"wood_consumption": 1, "food_production_per_stockpiled_food": 5, "max_food_production_per_stockpiled_food": 5}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_storage_capacity": 5}, "count": 1}, "ancient_quarry": {"display_name": "Ancient Quarry", "type": "quarry", "era": 2, "cost": {"wood": 4, "stone": 3}, "modifiers": {"miner_stone_production": 0.5}, "synergy_requirement": "stone", "synergy_node_active": true, "synergy_modifiers": {"district_stone_discount": 0.2, "city_stone_discount": 0.2}, "count": 1}, "ancient_ranch": {"display_name": "Ancient Ranch", "type": "ranch", "era": 2, "cost": {"wood": 5, "food": 3, "mounts": 2}, "modifiers": {"mounts_production": 2, "food_production": 1}, "synergy_requirement": "mounts", "synergy_node_active": true, "synergy_modifiers": {"mounts_production": 1}, "count": 1}, "ancient_sawmill": {"display_name": "Ancient Sawmill", "type": "sawmill", "era": 2, "cost": {"wood": 5, "stone": 2}, "modifiers": {"harvester_wood_production": 0.5}, "synergy_requirement": "wood", "synergy_node_active": true, "synergy_modifiers": {"district_wood_discount": 0.2, "city_wood_discount": 0.2}, "count": 1}, "ancient_school": {"display_name": "Ancient School", "type": "school", "era": 2, "cost": {"wood": 8, "stone": 2, "bronze": 1}, "modifiers": {"wood_consumption": 1, "research_production": 1}, "synergy_requirement": "research", "synergy_node_active": true, "synergy_modifiers": {}, "count": 1}, "ancient_workshop": {"display_name": "Ancient Workshop", "type": "workshop", "era": 2, "cost": {"wood": 2, "food": 1, "stone": 1, "bronze": 1}, "modifiers": {"wood_consumption": 1}, "synergy_requirement": "research", "synergy_node_active": true, "synergy_modifiers": {"mundane_unit_attack": 1}, "count": 1}, "latent_pylon": {"display_name": "Latent Pylon", "type": "latent_pylon", "era": 3, "cost": {"wood": 3, "stone": 3, "magic": 1}, "modifiers": {"magic_production": 1}, "count": 0}, "classical_barrow": {"display_name": "Classical Barrow", "type": "barrow", "era": 3, "cost": {"stone": 7, "magic": 6}, "modifiers": {"magic_consumption": 2, "nation_spell_cost": -2, "nation_spell_cost_minimum": 2}, "synergy_requirement": "magic", "synergy_node_active": true, "synergy_modifiers": {"ignore_magic_cost": true}, "count": 1}, "classical_brewery": {"display_name": "Classical Brewery", "type": "brewery", "era": 3, "cost": {"wood": 5, "stone": 3, "bronze": 3}, "alt_cost": {"wood": 5, "stone": 3, "iron": 1}, "modifiers": {"food_consumption": 1, "chance_to_gain_stability": 0.15}, "synergy_requirement": "narcotics", "synergy_node_active": false, "synergy_modifiers": {"chance_to_gain_stability": 0.1}, "count": 1}, "classical_church": {"display_name": "Classical Church", "type": "church", "era": 3, "cost": {"wood": 4, "stone": 5, "bronze": 1}, "modifiers": {}, "synergy_requirement": "wood", "synergy_node_active": true, "synergy_modifiers": {}, "count": 1}, "classical_dock": {"display_name": "Classical Dock", "type": "dock", "era": 3, "cost": {"wood": 3, "stone": 6, "food": 3, "bronze": 2}, "alt_cost": {"wood": 3, "stone": 6, "food": 3, "iron": 1}, "modifiers": {"stone_consumption": 1, "food_production": 2, "trade_distance_over_water": 3}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_production": 1}, "count": 1}, "classical_fishery": {"display_name": "Classical Fishery", "type": "fishery", "era": 3, "cost": {"food": 6, "wood": 4, "stone": 3}, "modifiers": {"stone_consumption": 1, "fisherman_food_production": 1, "food_production": 2, "food_production_per_naval_unit": 0.5}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_production": 2}, "count": 1}, "classical_fort": {"display_name": "Classical Fort", "type": "fort", "era": 3, "cost": {"wood": 4, "bronze": 3}, "alt_cost": {"stone": 5, "iron": 1}, "modifiers": {"bronze_consumption": 1}, "synergy_requirement": "bronze", "synergy_node_active": true, "synergy_modifiers": {}, "count": 2}, "classical_granary": {"display_name": "Classical Granary", "type": "granary", "era": 3, "cost": {"wood": 7, "stone": 5, "food": 3}, "modifiers": {"mounts_consumption": 1, "food_storage_capacity": 10, "farmer_food_production": 1}, "synergy_requirement": "food", "synergy_node_active": true, "synergy_modifiers": {"food_storage_capacity": 5}, "count": 1}, "classical_library": {"display_name": "Classical Library", "type": "library", "era": 3, "cost": {"wood": 10, "stone": 5}, "modifiers": {"wood_consumption": 2, "research_production_per_turns_with_library": 5, "max_research_production_per_turns_with_library": 4}, "synergy_requirement": "wood", "synergy_node_active": true, "synergy_modifiers": {}, "count": 1}, "classical_magic_site": {"display_name": "Classical Magic Site", "type": "magic_site", "era": 3, "cost": {"stone": 2, "magic": 6, "bronze": 2}, "alt_cost": {"stone": 2, "magic": 6, "iron": 1}, "modifiers": {"stone_consumption": 1, "magic_production": 1}, "synergy_requirement": "magic", "synergy_node_active": true, "synergy_modifiers": {"magic_production": 1}, "count": 1}, "classical_market": {"display_name": "Classical Market", "type": "market", "era": 3, "cost": {"money": 600, "wood": 5, "stone": 3}, "modifiers": {"mounts_consumption": 1, "trade_distance": 6, "trade_slots": 2, "merchant_money_production": 50, "money_income": 100}, "synergy_requirement": "luxury", "synergy_node_active": false, "synergy_modifiers": {"trade_distance": 6, "trade_slots": 2}, "count": 1}, "classical_mine": {"display_name": "Classical Mine", "type": "mine", "era": 3, "cost": {"wood": 6, "stone": 3, "bronze": 4}, "alt_cost": {"money": 250, "wood": 6, "stone": 3, "iron": 2}, "modifiers": {"stone_consumption": 1, "iron_production": 1, "metallurgist_iron_production": 1}, "synergy_requirement": "iron", "synergy_node_active": true, "synergy_modifiers": {"iron_production": 1}, "count": 1}, "classical_mint": {"display_name": "Classical Mint", "type": "mint", "era": 3, "cost": {"money": 750, "stone": 6, "bronze": 3}, "alt_cost": {"money": 750, "stone": 6, "iron": 1}, "modifiers": {"bronze_consumption": 1, "money_income_per_money_storage": 500, "max_money_income_per_money_storage": 400}, "synergy_requirement": "luxury", "synergy_node_active": false, "synergy_modifiers": {"max_money_income_per_money_storage": 400}, "count": 1}, "classical_quarry": {"display_name": "Classical Quarry", "type": "quarry", "era": 3, "cost": {"wood": 6, "stone": 3, "bronze": 2}, "alt_cost": {"wood": 6, "stone": 3, "iron": 1}, "modifiers": {"mounts_consumption": 1, "miner_stone_production": 1}, "synergy_requirement": "stone", "synergy_node_active": true, "synergy_modifiers": {"district_stone_discount": 0.25, "city_stone_discount": 0.25}, "count": 1}, "classical_ranch": {"display_name": "Classical Ranch", "type": "ranch", "era": 3, "cost": {"food": 4, "wood": 4, "mounts": 4, "bronze": 2}, "alt_cost": {"food": 4, "wood": 4, "mounts": 4, "iron": 1}, "modifiers": {"wood_consumption": 1, "mounts_production": 4, "food_production": 1, "miner_stone_production": 1}, "synergy_requirement": "mounts", "synergy_node_active": true, "synergy_modifiers": {"mounts_production": 1, "food_production": 1}, "count": 1}, "classical_sawmill": {"display_name": "Classical Sawmill", "type": "sawmill", "era": 3, "cost": {"wood": 3, "stone": 6, "bronze": 2}, "alt_cost": {"wood": 3, "stone": 6, "iron": 1}, "modifiers": {"mounts_consumption": 1, "harvester_wood_production": 1}, "synergy_requirement": "wood", "synergy_node_active": true, "synergy_modifiers": {"district_wood_discount": 0.25, "city_wood_discount": 0.25}, "count": 1}, "classical_workshop": {"display_name": "Classical Workshop", "type": "workshop", "era": 3, "cost": {"money": 350, "wood": 4, "stone": 2, "bronze": 1}, "alt_cost": {"money": 350, "wood": 4, "stone": 2, "iron": 1}, "modifiers": {"bronze_consumption": 1}, "synergy_requirement": "bronze", "synergy_node_active": true, "synergy_modifiers": {"mundane_unit_attack": 1}, "count": 1}}