/* styles.css */

.nav-right {
    display: flex;
    align-items: center; /* Centers vertically */
    justify-content: center;
    position: relative;
}

.nav-right img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #e0e0e0; /* Proper outline */
    box-shadow: 0px 0px 5px rgba(255, 255, 255, 0.2); /* Subtle glow effect */
}

body {
    font-family: Arial, sans-serif;
    background-color: #121212;
    color: #e0e0e0;
    margin: 0;
    padding: 0;
}

header {
    background: #222;
    color: #fff;
    padding: 20px;
    text-align: center;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #333;
    padding: 0.5rem 1rem;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
}

/* Each top-level item */
.nav-item {
  position: relative;
  margin-right: 1rem;
}

/* Links for top-level items */
.nav-item a {
  color: #8ab4f8;
  text-decoration: none;
  padding: 0.5rem 1rem;
  display: block;
  transition: background-color 0.3s, transform 0.1s;
}

/* Hover/Active styling for top-level links */
.nav-item a:hover,
.nav-item a.active {
  background-color: #444;
  transform: scale(1.05);
}

/* Dropdown content container - initially hidden */
.dropdown-content {
  display: none;
  position: absolute;
  top: 100%; /* Place below the parent */
  left: 0;
  background-color: #333;
  min-width: 160px;
  z-index: 1000;
  list-style: none;
  margin: 0;
  padding: 0;
  border: 1px solid #444;
  border-radius: 4px;
}

/* Each dropdown item */
.dropdown-content li a {
  padding: 0.5rem 1rem;
  white-space: nowrap;
}

/* Show dropdown on hover */
.nav-item:hover .dropdown-content {
  display: block;
}

.navbar a {
    color: #8ab4f8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
	transition: background-color 0.3s, transform 0.1s;
    display: inline-block;
}

.navbar a.active,
.navbar a:hover {
    background-color: #444;
    transform: scale(1.05);
}

a {
    color: #8ab4f8; /* Light blue for better contrast */
    text-decoration: none;
}

a:hover {
    color: #c3dbff; /* Slightly lighter blue on hover */
    text-decoration: underline;
}

a:visited {
    color: #8ab4f8; /* Keep it the same as normal links */
}

.container {
    width: 80%;
    margin: auto;
    overflow: hidden;
}

.content {
    background: #1e1e1e;
    padding: 20px;
    margin-top: 20px;
    border-radius: 8px;
    color: #e0e0e0;
}

h1, h2 {
    color: #fff;
}

/* Jobs table styling */
.jobs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 2rem;
}

.jobs-table thead th {
    background-color: #333;
    color: #fff;
    padding: 8px;
    border: 1px solid #444;
    text-align: left;
}

.jobs-table tbody td {
    background-color: #222;
    color: #e0e0e0;
    padding: 8px;
    border: 1px solid #444;
}

/* Alternate row background */
.jobs-table tbody tr:nth-child(even) {
    background-color: #1e1e1e;
}

/* Hover effect */
.jobs-table tbody tr:hover {
    background-color: #2a2a2a;
}

/* Nation Page Container */
.nation-page-container {
  width: 80%;
  margin: 0 auto;
  padding-bottom: 2rem;
  color: #e0e0e0;
}

/* Header / Title Bar */
.header-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem 0;
  border-bottom: 1px solid #444;
  padding-bottom: 1rem;
}
.header-bar h1 {
  margin: 0;
  font-size: 2rem;
}
.nation-summary {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #8ab4f8;
}
.edit-button {
  margin-top: 0.5rem;
  background-color: #444;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  color: #8ab4f8;
  text-decoration: none;
  transition: background-color 0.3s, transform 0.1s;
}
.edit-button:hover {
  background-color: #555;
  transform: scale(1.05);
}

/* General Overview Section */
.general-overview {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin: 1rem 0;
}

/* Government & Policies Section */
.gov-policies {
  margin: 1rem 0;
  background-color: #1e1e1e;
  padding: 1rem;
  border-radius: 8px;
}
.gov-policies h2 {
  margin-top: 0;
}

/* Job Assignments Section */
.jobs-section {
  margin: 2rem 0;
}
.jobs-section h2 {
  margin-bottom: 1rem;
}

.nation-summary span {
  display: inline-block;
  margin-right: 1rem;
  font-size: 0.9rem;
}

.nation-summary a {
  color: #8ab4f8;
  text-decoration: none;
}

.nation-summary a:hover {
  text-decoration: underline;
  color: #c3dbff;
}

.main-columns {
  display: flex;
  flex-wrap: wrap; /* ensures wrapping on narrow screens */
  gap: 1rem;
}

/* Each column gets a background and a bit of padding */
.info-column {
  flex: 1 1 26%; /* each column roughly half the width */
  background-color: #1e1e1e;
  padding: 1rem;
  border-radius: 8px;
}

/* Make the tables more compact */
.info-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0; /* remove extra margins if desired */
}

.info-table th,
.info-table td {
  border: 1px solid #444;
  padding: .5em;
  vertical-align: middle;
}

.info-table th {
  background-color: #333;
  color: #fff;
  text-align: center;
}

.info-table td {
  background-color: #222;
  color: #e0e0e0;
  text-align: left;
}

.compact-table {
  border-collapse: collapse;
  margin: 0; /* remove extra margins if desired */
}

.compact-table th,
.compact-table td {
  border: 1px solid #444;
  padding: .5em;
  vertical-align: middle;
}

.compact-table th {
  background-color: #333;
  color: #fff;
  text-align: right;
}

.compact-table td {
  background-color: #222;
  color: #e0e0e0;
  text-align: left;
}

/* Districts Grid Container */
.districts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

/* District Slot */
.district-slot {
  background-color: #1e1e1e;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.district-slot h3 {
  margin: 0.5rem 0;
  font-size: 1.1rem;
  color: #fff;
}

.district-slot p {
  margin: 0.3rem 0;
  font-size: 0.9rem;
  color: #e0e0e0;
}

/* Empty Slot styling */
.empty-slot {
  font-style: italic;
  color: #888;
}

/* For the select inputs on the edit page */
.district-slot select {
  width: 100%;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: #222;
  color: #e0e0e0;
  border: 1px solid #444;
}

.resources-section {
  margin-top: 2rem;
}

.resources-section h2 {
  margin-bottom: 1rem;
}

.resources-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.resources-table th,
.resources-table td {
  border: 1px solid #444;
  padding: 8px;
  text-align: center;
  background-color: #222;
  color: #e0e0e0;
}

.resources-table th {
  background-color: #333;
  color: #fff;
}

.resources-header-row th {
  background-color: #555; /* Distinguish section headers */
  font-weight: bold;
  font-size: 1rem;
}

/* Example highlight for negative excess */
.resources-table td.excess-critical-negative {
  background-color: #451b1b; /* dark red background */
  color: #ff9f9f;            /* lighter red text */
}

/* Example highlight for negative excess */
.resources-table td.excess-negative {
  background-color: #45451b; /* dark yellow background */
  color: #ffff9f;            /* lighter yellow text */
}

/* Example highlight for positive excess */
.resources-table td.excess-positive {
  background-color: #1b4522; /* dark green background */
  color: #afffbd;            /* light green text */
}

/* Example highlight for overflow excess */
.resources-table td.excess-overflow {
  background-color: #45331b; /* dark orange background */
  color: #ffbd9f;            /* light orange text */
}

.pops-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.pops-table th,
.pops-table td {
  border: 1px solid #444;
  padding: 6px;
  text-align: left;
  background-color: #222;
  color: #e0e0e0;
}

.pops-table th {
  background-color: #333;
  color: #fff;
}

.pops-table thead {
  background-color: #333;
}

.pops-table tr:nth-child(even) {
  background-color: #1e1e1e;
}

.demographics-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.demographics-table th,
.demographics-table td {
  border: 1px solid #444;
  padding: 8px;
  text-align: left;
  background-color: #222;
  color: #e0e0e0;
}

.demographics-table th {
  background-color: #333;
  color: #fff;
}

.demographics-table a {
  color: #8ab4f8;
  text-decoration: none;
}

.demographics-table a:hover {
  text-decoration: underline;
  color: #c3dbff;
}

.multiline-text {
  white-space: pre-line; /* Preserves line breaks and wraps text */
  background-color: #1e1e1e; /* Match your theme */
  padding: 1rem;
  border: 1px solid #444;
  border-radius: 8px;
  margin-top: 1rem;
}

/* Table wrapper for horizontal scrolling */
.table-wrapper {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 2rem;
    /* Smooth scrolling on touch devices */
    -webkit-overflow-scrolling: touch;
}

/* Adjust info-table styles */
.info-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    /* Prevent table from shrinking below its natural size */
    white-space: nowrap;
    min-width: 800px; /* Minimum width before scrolling starts */
}

.info-table th,
.info-table td {
    border: 1px solid #444;
    padding: .5em;
    vertical-align: middle;
    /* Allow text wrapping for specific columns */
    white-space: normal;
}

/* Allow specific columns to wrap */
.info-table td.wrap-content {
    white-space: normal;
    min-width: 200px; /* Minimum width for wrapped columns */
    max-width: 300px; /* Maximum width for wrapped columns */
}

/* Style for the changes column specifically */
.changes-column {
    white-space: normal !important;
    min-width: 250px;
    max-width: 400px;
}

/* Style for the reason column */
.reason-column {
    white-space: normal !important;
    min-width: 150px;
    max-width: 300px;
    word-break: break-word; /* Breaks long words and URLs */
    overflow-wrap: break-word;
}

/* Style for Discord links in reason column */
.reason-column a {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #007bff;
    text-decoration: none;
}

.reason-column a:hover {
    text-decoration: underline;
}

/* Ensure action buttons don't wrap */
.action-buttons {
    white-space: nowrap;
    min-width: 120px;
}

/* Make buttons more compact in tables */
.btn {
    padding: 4px 8px;
    margin: 2px;
    display: inline-block;
    font-size: 0.9em;
}

.btn-approve {
    background-color: #1b4522;
    color: #afffbd;
}

.btn-deny {
    background-color: #451b1b;
    color: #ff9f9f;
}

/* Units page specific styles */
.units-container {
    width: 95%;
    max-width: 1800px;
    margin: 0 auto;
    padding: 1rem;
}

.unit-category {
    margin-bottom: 2rem;
    background: #1e1e1e;
    border-radius: 8px;
    padding: 1rem;
}

.unit-category h2 {
    color: #fff;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #333;
}

.unit-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.unit-card {
    position: relative;
    transition: transform 0.2s ease-in-out;
    cursor: pointer;
}

.unit-card:hover {
    transform: scale(1.05);
}

.unit-card img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    display: block;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    padding: 40px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    overflow: auto;
}

.modal-content {
    display: block;
    margin: auto;
    max-width: 90%;
    max-height: 90vh;
    object-fit: contain;
}

.modal-close {
    position: fixed;
    right: 35px;
    top: 15px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

.modal-close:hover,
.modal-close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .units-container {
        width: 100%;
        padding: 0.5rem;
    }
    
    .unit-category {
        margin-bottom: 1rem;
        padding: 0.5rem;
    }
    
    .unit-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .modal {
        padding: 20px;
    }

    .modal-close {
        right: 20px;
        top: 10px;
    }
}

/* For very small screens */
@media (max-width: 480px) {
    .unit-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* Changes table styling */
.changes-column {
    max-width: 400px;
    min-width: 250px;
}

.reason-column {
    max-width: 200px;
}

.wrap-content {
    white-space: pre-wrap;
    word-break: break-word;
}

.change-item {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

.change-item:last-child {
    border-bottom: none;
}

.change-label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #4CAF50;
}

.dict-change {
    margin-left: 15px;
    margin-bottom: 5px;
}

.dict-key {
    color: #888;
    margin-right: 5px;
}

.array-dict-item {
    margin-left: 15px;
    margin-bottom: 10px;
    padding: 5px;
    border-left: 2px solid #444;
}

.array-index {
    font-weight: bold;
    color: #888;
    margin-bottom: 5px;
}

.array-change {
    margin-left: 15px;
}

.array-before, .array-after {
    margin-bottom: 5px;
}

.simple-data {
    margin-left: 15px;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn {
    padding: 3px 8px;
    border-radius: 3px;
    text-decoration: none;
    font-size: 0.9em;
}

.btn-approve {
    background-color: #4CAF50;
    color: white;
}

.btn-deny {
    background-color: #f44336;
    color: white;
}

/* Resource table specific styling */
.resource-table {
    width: 100%;
    max-width: 400px;
}

.resource-table th {
    width: 40%;
    text-align: right;
    padding-right: 10px;
    font-weight: bold;
    color: #e0e0e0;
}

.resource-table td {
    width: 60%;
    text-align: left;
}

/* Highlight positive and negative values in resource tables */
.resource-table td.positive {
    color: #afffbd;
}

.resource-table td.negative {
    color: #ff9f9f;
}

/* Navigation tabs for changes pages */
.nav-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #333;
}

.nav-tabs a {
    padding: 10px 15px;
    text-decoration: none;
    color: #ccc;
    border-radius: 5px 5px 0 0;
    margin-right: 5px;
}

.nav-tabs a:hover {
    background-color: #333;
}

.nav-tabs a.active {
    background-color: #333;
    color: #4CAF50;
    border-bottom: 2px solid #4CAF50;
}

/* Pagination styles */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    background-color: #222;
    border-radius: 5px;
}

.pagination-controls {
    display: flex;
    gap: 5px;
}

.pagination-btn {
    padding: 5px 10px;
    background-color: #333;
    color: #ccc;
    text-decoration: none;
    border-radius: 3px;
    font-size: 0.9em;
}

.pagination-btn:hover {
    background-color: #444;
}

.pagination-current {
    padding: 5px 10px;
    background-color: #4CAF50;
    color: white;
    border-radius: 3px;
    font-size: 0.9em;
}

/* Breadcrumbs styling */
.breadcrumbs {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #222;
    border-radius: 5px;
}

.breadcrumbs a {
    color: #4CAF50;
    text-decoration: none;
}

.breadcrumbs a:hover {
    text-decoration: underline;
}

/* Table wrapper */
.table-wrapper {
    margin-top: 15px;
}

/* Breadcrumbs and action links */
.breadcrumbs {
    margin-bottom: 15px;
}

.breadcrumbs a {
    color: #4CAF50;
    text-decoration: none;
}

.breadcrumbs a:hover {
    text-decoration: underline;
}

.action-links {
    margin: 15px 0;
    display: flex;
    gap: 15px;
}

.action-links a {
    display: inline-block;
    padding: 5px 10px;
    background-color: #333;
    color: #fff;
    text-decoration: none;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.action-links a:hover {
    background-color: #444;
}

/* Tech Tree Styling */
.tech-section {
    margin-top: 2rem;
}

.tech-section h2 {
    margin-bottom: 1rem;
}

#cy {
    position: relative;
    width: 100%;
    height: 800px;
    background-color: #222;
}

.cy-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 999;
}

.cy-controls button {
    background: #333;
    color: white;
    border: 1px solid #555;
    padding: 5px 10px;
    margin: 0 2px;
    cursor: pointer;
}

.cy-controls button:hover {
    background: #444;
}

#tech-info-panel {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 300px;
    background: rgba(40, 40, 40, 0.9);
    border: 1px solid #555;
    padding: 15px;
    border-radius: 5px;
    color: white;
    z-index: 1000;
    display: none;
    max-height: 400px;
    overflow-y: auto;
}

#tech-info-panel h3 {
    margin-top: 0;
    border-bottom: 1px solid #555;
    padding-bottom: 5px;
}

#tech-info-panel button {
    background: #333;
    color: white;
    border: 1px solid #555;
    padding: 5px 10px;
    margin-top: 10px;
    cursor: pointer;
}

#tech-info-panel button:hover {
    background: #444;
}

/* Tech Investment Form Styling */

.tech-node-input-container {
  display: flex;
  align-items: center;
  color: black;
}

.tech-node-input {
  width: 16px;
  padding: 0.25rem;
  margin: 0 0.5rem;
  background-color: #555;
  border: 1px solid #666;
  color: #fff;
  border-radius: 3px;
  -moz-appearance: textfield;
}

.tech-node-input::-webkit-outer-spin-button,
.tech-node-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.form-actions {
    margin-top: 1rem;
    text-align: right;
}

.btn-primary {
    padding: 0.5rem 1rem;
    background-color: #1f6bd8;
    border: none;
    color: white;
    border-radius: 3px;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #1a5cb8;
}

/* Expandable Sections Styling */
.expandable-sections {
    margin-bottom: 2rem;
}

.expandable-section {
    margin-bottom: 1rem;
    border: 1px solid #444;
    border-radius: 8px;
    overflow: hidden;
    background-color: #1e1e1e;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #333;
    cursor: pointer;
    transition: background-color 0.2s;
}

.section-header:hover {
    background-color: #444;
}

.section-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: #fff;
}

.toggle-icon {
    font-size: 1rem;
    color: #8ab4f8;
    transition: transform 0.3s;
}

.section-content {
    display: none;
    padding: 1rem;
    border-top: 1px solid #444;
}

/* When section is expanded */
.section-expanded .section-content {
    display: block;
}

.section-expanded .toggle-icon {
    transform: rotate(180deg);
}

/* Form actions styling */
.form-actions {
    margin-top: 2rem;
    text-align: left;
}

.reason-field {
    margin-bottom: 1rem;
    max-width: 100%;
}

.reason-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.reason-field textarea {
    width: 100%;
    min-height: 100px;
    background-color: #222;
    color: #e0e0e0;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 0.5rem;
}

.button-group {
    display: flex;
    gap: 10px;
}

/* Map viewer styles */
.map-viewer {
    width: 100%;
    height: 70vh;
    background-color: #222;
    border: 1px solid #444;
    margin: 1rem 0;
    position: relative;
}

.map-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.button-group {
    display: flex;
    gap: 0.5rem;
}

.map-btn {
    padding: 0.5rem 1rem;
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.map-btn:hover {
    background-color: #444;
}

.map-btn.active {
    background-color: #0066cc;
    border-color: #0055aa;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#opacity-slider {
    width: 150px;
    background-color: #333;
    height: 8px;
    -webkit-appearance: none;
    border-radius: 4px;
}

#opacity-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background-color: #0066cc;
    border-radius: 50%;
    cursor: pointer;
}

#opacity-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background-color: #0066cc;
    border-radius: 50%;
    cursor: pointer;
}

#opacity-value {
    min-width: 40px;
    text-align: right;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .map-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .map-viewer {
        height: 50vh;
    }
}

/* Chart container styles */
.chart-container {
    width: 100%;
    height: 400px;
    margin: 20px 0;
    background-color: #222;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 20px;
    box-sizing: border-box;
}

/* Make sure the chart is responsive */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }
}

.resource-desires-table {
    width: 100%;
    margin-top: 20px;
    border-collapse: collapse;
}

.resource-desires-table th {
    background-color: #333;
    padding: 8px;
    text-align: left;
    border: 1px solid #444;
    color: #fff;
}

.resource-desires-table td {
    padding: 8px;
    border: 1px solid #444;
    background-color: #222;
    color: #e0e0e0;
}

.resource-desires-table tr:nth-child(even) td {
    background-color: #1e1e1e;
}

.resource-desires-table tr:hover td {
    background-color: #2a2a2a;
}

/* Add styling for buy/sell indicators with different urgency levels */
.resource-desires-table .buy-indicator {
    color: #afffbd; /* Light green for regular buy */
}

.resource-desires-table .sell-indicator {
    color: #ff9f9f; /* Light red for regular sell */
}

.resource-desires-table .need-buy-indicator {
    color: #00ff00; /* Bright green for urgent buy needs */
    font-weight: bold;
}

.resource-desires-table .need-sell-indicator {
    color: #ff3333; /* Bright red for urgent sell needs */
    font-weight: bold;
}

.era-display {
    margin-left: 1rem;
    color: #e0e0e0;
    font-size: 0.9rem;
    white-space: nowrap;
}

.session-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 1rem;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .session-selector {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .era-display {
        margin-left: 0;
        margin-top: 0.5rem;
    }
}

#session-select {
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 0.3rem 0.5rem;
}

.map-viewer.loading::after {
    content: "Loading map...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
}

.restricted-field {
    color: #888;
    font-style: italic;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.9em;
}

.field-change {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.field-label {
    font-weight: bold;
    margin-bottom: 4px;
}

.drag-handle {
    cursor: grab;
    user-select: none;
    color: #888;
    margin-right: 8px;
    font-weight: bold;
}

.drag-handle:active {
    cursor: grabbing;
}

.sortable-row.sortable-ghost {
    opacity: 0.4;
}

.sortable-row.sortable-chosen {
    background-color: #333;
}
