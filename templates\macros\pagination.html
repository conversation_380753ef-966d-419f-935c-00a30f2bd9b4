{% macro render_pagination(current_page, total_pages, base_url) %}
<div class="pagination">
    <span>Page {{ current_page }} of {{ total_pages }}</span>
    <div class="pagination-controls">
        {% if current_page > 1 %}
            <a href="{{ base_url }}/page/1" class="pagination-btn">First</a>
            <a href="{{ base_url }}/page/{{ current_page - 1 }}" class="pagination-btn">&laquo; Previous</a>
        {% endif %}
        
        {% set start_page = current_page - 2 if current_page - 2 > 0 else 1 %}
        {% set end_page = current_page + 3 if current_page + 3 <= total_pages else total_pages + 1 %}
        {% for p in range(start_page, end_page) %}
            {% if p == current_page %}
                <span class="pagination-current">{{ p }}</span>
            {% else %}
                <a href="{{ base_url }}/page/{{ p }}" class="pagination-btn">{{ p }}</a>
            {% endif %}
        {% endfor %}
        
        {% if current_page < total_pages %}
            <a href="{{ base_url }}/page/{{ current_page + 1 }}" class="pagination-btn">Next &raquo;</a>
            <a href="{{ base_url }}/page/{{ total_pages }}" class="pagination-btn">Last</a>
        {% endif %}
    </div>
</div>
{% endmacro %}

{% macro render_field_change(before_value, after_value, view_access_level, field_schema=None, collection_name=None, preview_references=None) %}
    {% if field_schema and field_schema.get("view_access_level", 0) > view_access_level %}
        <span class="restricted-field">Restricted content</span>
    {% elif before_value is mapping and after_value is mapping %}
        {# Handle dictionaries - only show changed keys #}
        {% set changed_keys = [] %}
        {% set all_keys = (before_value.keys()|list + after_value.keys()|list)|unique|list %}
        {% for dict_key in all_keys %}
            {% if before_value.get(dict_key) != after_value.get(dict_key) %}
                {% set _ = changed_keys.append(dict_key) %}
            {% endif %}
        {% endfor %}
        
        {% if changed_keys|length > 0 %}
            <div class="dict-changes">
                {% for dict_key in changed_keys %}
                    <div class="dict-change-item">
                        <span class="dict-key">{{ dict_key }}:</span> 
                        {{ render_field_change(before_value.get(dict_key), after_value.get(dict_key), view_access_level, field_schema, collection_name, preview_references) }}
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <span class="no-changes">No changes</span>
        {% endif %}
    {% elif before_value is sequence and before_value is not string and after_value is sequence and after_value is not string %}
        <div class="array-changes">
            {% if (before_value and before_value[0] is mapping) or (after_value and after_value[0] is mapping) %}
                {# Show modified items #}
                {% for i in range([before_value|length, after_value|length]|min) %}
                    {% set before_item = before_value[i] if i < before_value|length else {} %}
                    {% set after_item = after_value[i] if i < after_value|length else {} %}
                    {% set item_changed = false %}
                    
                    {% if before_item is mapping and after_item is mapping %}
                        {% for item_key in (before_item.keys()|list + after_item.keys()|list)|unique|list %}
                            {% if before_item.get(item_key) != after_item.get(item_key) %}
                                {% set item_changed = true %}
                            {% endif %}
                        {% endfor %}
                        
                        {% if item_changed %}
                            <div class="array-item-changed">
                                <div class="array-index">Item {{ i+1 }}:</div>
                                {{ render_field_change(before_item, after_item, view_access_level, field_schema, collection_name, preview_references) }}
                            </div>
                        {% endif %}
                    {% elif before_item != after_item %}
                        <div class="array-item-changed">
                            <div class="array-index">Item {{ i+1 }}:</div>
                            <span class="simple-change">
                                {% if before_item is none %}None{% elif before_item == "" %}""{% elif before_item == 0 %}0{% else %}{{ before_item }}{% endif %} → 
                                {% if after_item is none %}None{% elif after_item == "" %}""{% elif after_item == 0 %}0{% else %}{{ after_item }}{% endif %}
                            </span>
                        </div>
                    {% endif %}
                {% endfor %}
                
                {# Show added items #}
                {% if after_value|length > before_value|length %}
                    <div class="array-items-added">
                        <span class="array-action">Added:</span>
                        {% for i in range(before_value|length, after_value|length) %}
                            <div class="array-item-added">
                                <div class="array-index">Item {{ i+1 }}:</div>
                                {% if after_value[i] is mapping %}
                                    {% for dict_key, dict_value in after_value[i].items() %}
                                        <div class="dict-change-item">
                                            <span class="dict-key">{{ dict_key }}:</span> 
                                            <span class="dict-value">
                                                {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                            </span>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <span class="dict-value">
                                        {% if after_value[i] is none %}None{% elif after_value[i] == "" %}""{% elif after_value[i] == 0 %}0{% else %}{{ after_value[i] }}{% endif %}
                                    </span>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                
                {# Show removed items #}
                {% if before_value|length > after_value|length %}
                    <div class="array-items-removed">
                        <span class="array-action">Removed:</span>
                        {% for i in range(after_value|length, before_value|length) %}
                            <div class="array-item-removed">
                                <div class="array-index">Item {{ i+1 }}:</div>
                                {% if before_value[i] is mapping %}
                                    {% for dict_key, dict_value in before_value[i].items() %}
                                        <div class="dict-change-item">
                                            <span class="dict-key">{{ dict_key }}:</span> 
                                            <span class="dict-value">
                                                {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                            </span>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <span class="dict-value">
                                        {% if before_value[i] is none %}None{% elif before_value[i] == "" %}""{% elif before_value[i] == 0 %}0{% else %}{{ before_value[i] }}{% endif %}
                                    </span>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% else %}
                {# Handle simple arrays #}
                {% if before_value|length != after_value|length or before_value != after_value %}
                    <div class="array-before">
                        <span class="array-label">Before:</span> 
                        {% if before_value %}
                            [{% for item in before_value %}{% if item is none %}None{% elif item == "" %}""{% elif item == 0 %}0{% else %}{{ item }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}]
                        {% else %}
                            []
                        {% endif %}
                    </div>
                    <div class="array-after">
                        <span class="array-label">After:</span> 
                        {% if after_value %}
                            [{% for item in after_value %}{% if item is none %}None{% elif item == "" %}""{% elif item == 0 %}0{% else %}{{ item }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}]
                        {% else %}
                            []
                        {% endif %}
                    </div>
                {% else %}
                    <span class="no-changes">No changes</span>
                {% endif %}
            {% endif %}
        </div>
    {% elif field_schema and field_schema.bsonType == "linked_object" and collection_name and preview_references %}
        {# Handle linked objects with references #}
        {% set before_id = before_value|string if before_value else None %}
        {% set after_id = after_value|string if after_value else None %}
        
        <span class="linked-object-change">
            {% if before_id and before_id in preview_references[collection_name] %}
                <a href="{{ preview_references[collection_name][before_id]['link'] }}">{{ preview_references[collection_name][before_id]['name'] }}</a>
            {% else %}
                None
            {% endif %}
            →
            {% if after_id and after_id in preview_references[collection_name] %}
                <a href="{{ preview_references[collection_name][after_id]['link'] }}">{{ preview_references[collection_name][after_id]['name'] }}</a>
            {% else %}
                None
            {% endif %}
        </span>
    {% else %}
        {# Simple value change #}
        <span class="simple-change">
            {% if before_value is none %}None{% elif before_value == "" %}""{% elif before_value == 0 %}0{% else %}{{ before_value }}{% endif %} → 
            {% if after_value is none %}None{% elif after_value == "" %}""{% elif after_value == 0 %}0{% else %}{{ after_value }}{% endif %}
        </span>
    {% endif %}
{% endmacro %}

{% macro render_changes_preview(before_data, after_data, target_schema, preview_references, view_access_level) %}
    <div class="changes-preview">
        {% if before_data is mapping and after_data is mapping %}
            {% set all_keys = (before_data.keys()|list + after_data.keys()|list)|unique|list %}
            {% for key in all_keys %}
                {% set before_value = before_data.get(key) %}
                {% set after_value = after_data.get(key) %}
                {% set field_schema = target_schema.properties.get(key, {}) %}
                {% set required_view_level = field_schema.get("view_access_level", 0) %}
                
                {% if before_value != after_value or (key in before_data) != (key in after_data) %}
                    <div class="field-change">
                        <div class="field-label">{{ field_schema.get("label", key) }}:</div>
                        <div class="field-value">
                            {% if required_view_level > view_access_level %}
                                <span class="restricted-field">Restricted content</span>
                            {% elif (key == "districts" or key == "cities") and before_value is sequence and after_value is sequence %}
                                {% if after_value|length > before_value|length %}
                                    <span>Added: {{ after_value|length - before_value|length }} new {{ key }}</span>
                                {% endif %}
                                
                                {# Show modified items #}
                                {% for i in range([before_value|length, after_value|length]|min) %}
                                    {% set before_item = before_value[i] if i < before_value|length else {} %}
                                    {% set after_item = after_value[i] if i < after_value|length else {} %}
                                    
                                    {# Direct comparison of items #}
                                    {% if before_item != after_item %}
                                        <div class="array-item-changed">
                                            <div class="array-index">{{ key|capitalize }} {{ i+1 }}:</div>
                                            <div class="dict-changes">
                                                {% if before_item is mapping and after_item is mapping %}
                                                    {% for item_key in (before_item.keys()|list + after_item.keys()|list)|unique|list %}
                                                        {% if before_item.get(item_key) != after_item.get(item_key) %}
                                                            <div class="dict-change-item">
                                                                <span class="dict-key">{{ item_key }}:</span>
                                                                <span class="dict-value">
                                                                    {% if before_item.get(item_key) is none %}None{% elif before_item.get(item_key) == "" %}""{% elif before_item.get(item_key) == 0 %}0{% else %}{{ before_item.get(item_key) }}{% endif %} → 
                                                                    {% if after_item.get(item_key) is none %}None{% elif after_item.get(item_key) == "" %}""{% elif after_item.get(item_key) == 0 %}0{% else %}{{ after_item.get(item_key) }}{% endif %}
                                                                </span>
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% else %}
                                                    <div class="dict-change-item">
                                                        <span class="dict-value">
                                                            {% if before_item is none %}None{% elif before_item == "" %}""{% elif before_item == 0 %}0{% else %}{{ before_item }}{% endif %} → 
                                                            {% if after_item is none %}None{% elif after_item == "" %}""{% elif after_item == 0 %}0{% else %}{{ after_item }}{% endif %}
                                                        </span>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                
                                {# Show added items #}
                                {% if after_value|length > before_value|length %}
                                    <div class="array-items-added">
                                        <span class="array-action">Added:</span>
                                        {% for i in range(before_value|length, after_value|length) %}
                                            <div class="array-item-added">
                                                <div class="array-index">{{ key|capitalize }} {{ i+1 }}:</div>
                                                {% if after_value[i] is mapping %}
                                                    {% for dict_key, dict_value in after_value[i].items() %}
                                                        <div class="dict-change-item">
                                                            <span class="dict-key">{{ dict_key }}:</span> 
                                                            <span class="dict-value">
                                                                {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                                            </span>
                                                        </div>
                                                    {% endfor %}
                                                {% else %}
                                                    <span class="dict-value">
                                                        {% if after_value[i] is none %}None{% elif after_value[i] == "" %}""{% elif after_value[i] == 0 %}0{% else %}{{ after_value[i] }}{% endif %}
                                                    </span>
                                                {% endif %}
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                
                                {# Show removed items #}
                                {% if before_value|length > after_value|length %}
                                    <div class="array-items-removed">
                                        <span class="array-action">Removed:</span>
                                        {% for i in range(after_value|length, before_value|length) %}
                                            <div class="array-item-removed">
                                                <div class="array-index">{{ key|capitalize }} {{ i+1 }}:</div>
                                                {% if before_value[i] is mapping %}
                                                    {% for dict_key, dict_value in before_value[i].items() %}
                                                        <div class="dict-change-item">
                                                            <span class="dict-key">{{ dict_key }}:</span> 
                                                            <span class="dict-value">
                                                                {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                                            </span>
                                                        </div>
                                                    {% endfor %}
                                                {% else %}
                                                    <span class="dict-value">
                                                        {% if before_value[i] is none %}None{% elif before_value[i] == "" %}""{% elif before_value[i] == 0 %}0{% else %}{{ before_value[i] }}{% endif %}
                                                    </span>
                                                {% endif %}
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            {% elif key not in before_data %}
                                {# Field was added #}
                                <span class="field-added">Added: 
                                    {% if after_value is mapping %}
                                        <div class="dict-added">
                                            {% for dict_key, dict_value in after_value.items() %}
                                                <div class="dict-change-item">
                                                    <span class="dict-key">{{ dict_key }}:</span> 
                                                    <span class="dict-value">
                                                        {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                                    </span>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% elif after_value is sequence and after_value is not string %}
                                        <div class="array-added">
                                            {% if after_value and after_value[0] is mapping %}
                                                {% for item in after_value %}
                                                    <div class="array-item-added">
                                                        <div class="array-index">Item {{ loop.index }}:</div>
                                                        {% for dict_key, dict_value in item.items() %}
                                                            <div class="dict-change-item">
                                                                <span class="dict-key">{{ dict_key }}:</span> 
                                                                <span class="dict-value">
                                                                    {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                                                </span>
                                                            </div>
                                                        {% endfor %}
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                [{% for item in after_value %}{% if item is none %}None{% elif item == "" %}""{% elif item == 0 %}0{% else %}{{ item }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}]
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        {% if after_value is none %}None{% elif after_value == "" %}""{% elif after_value == 0 %}0{% else %}{{ after_value }}{% endif %}
                                    {% endif %}
                                </span>
                            {% elif key not in after_data %}
                                {# Field was removed #}
                                <span class="field-removed">Removed: 
                                    {% if before_value is mapping %}
                                        <div class="dict-removed">
                                            {% for dict_key, dict_value in before_value.items() %}
                                                <div class="dict-change-item">
                                                    <span class="dict-key">{{ dict_key }}:</span> 
                                                    <span class="dict-value">
                                                        {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                                    </span>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% elif before_value is sequence and before_value is not string %}
                                        <div class="array-removed">
                                            {% if before_value and before_value[0] is mapping %}
                                                {% for item in before_value %}
                                                    <div class="array-item-removed">
                                                        <div class="array-index">Item {{ loop.index }}:</div>
                                                        {% for dict_key, dict_value in item.items() %}
                                                            <div class="dict-change-item">
                                                                <span class="dict-key">{{ dict_key }}:</span> 
                                                                <span class="dict-value">
                                                                    {% if dict_value is none %}None{% elif dict_value == "" %}""{% elif dict_value == 0 %}0{% else %}{{ dict_value }}{% endif %}
                                                                </span>
                                                            </div>
                                                        {% endfor %}
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                [{% for item in before_value %}{% if item is none %}None{% elif item == "" %}""{% elif item == 0 %}0{% else %}{{ item }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}]
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        {% if before_value is none %}None{% elif before_value == "" %}""{% elif before_value == 0 %}0{% else %}{{ before_value }}{% endif %}
                                    {% endif %}
                                </span>
                            {% else %}
                                {% set field_schema = target_schema.properties.get(key, {}) %}
                                {% set collection_name = field_schema.collections[0] if field_schema.get('bsonType') == "linked_object" and field_schema.get('collections') else None %}
                                {{ render_field_change(before_value, after_value, view_access_level, field_schema, collection_name, preview_references) }}
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            {% endfor %}
        {% else %}
            <div class="no-changes">No changes</div>
        {% endif %}
    </div>
{% endmacro %}

{% macro render_changes_table(changes, preview_references, target_schemas, view_access_level, show_actions=false) %}
<table class="info-table">
    <thead>
        <tr>
            <th>ID</th>
            <th>Target Collection</th>
            <th>Target</th>
            <th>Change Type</th>
            <th>Status</th>
            <th class="changes-column">Changes</th>
            <th class="reason-column">Reason</th>
            <th>Time</th>
            <th>User</th>
            {% if show_actions and g.user.is_admin %}
            <th>Actions</th>
            {% endif %}
        </tr>
    </thead>
    <tbody>
        {% for change in changes %}
            <tr>
                <td><a href="/changes/item/{{ change._id }}">{{ change._id }}</a></td>
                <td>{{ change.target_collection }}</td>
                <td>
                    {% if change.target_collection in preview_references and change.target|string in preview_references[change.target_collection] %}
                        <a href="{{ preview_references[change.target_collection][change.target|string]['link'] }}">
                            {{ preview_references[change.target_collection][change.target|string]['name'] }}
                        </a>
                    {% else %}
                        {{ change.target }}
                    {% endif %}
                </td>
                <td>{{ change.change_type }}</td>
                <td>{{ change.status }}</td>
                <td class="changes-column wrap-content">
                    {{ render_changes_preview(change.before_requested_data, change.after_requested_data, target_schemas[change.target_collection], preview_references, view_access_level) }}
                </td>
                <td class="reason-column wrap-content">
                    {{ change.request_reason|format_discord_link|safe }}
                </td>
                {% if change.last_modified_time is defined and change.last_modified_time is not none and change.last_modified_time|string is not string and change.last_modified_time.__class__.__name__ == 'datetime' %}
                    <td>{{ change.last_modified_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                {% elif change.time_requested is defined and change.time_requested is not none and change.time_requested|string is not string and change.time_requested.__class__.__name__ == 'datetime' %}
                    <td>{{ change.time_requested.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                {% else %}
                    <td>{{ change.time_requested }}</td>
                {% endif %}
                <td>
                    {% if "requester" in preview_references and change.requester|string in preview_references["requester"] %}
                        <a href="{{ preview_references['requester'][change.requester|string]['link'] }}">
                            {{ preview_references['requester'][change.requester|string]['name'] }}
                        </a>
                    {% endif %}
                </td>
                {% if show_actions and g.user.is_admin %}
                <td class="action-buttons">
                    <a href="/changes/item/{{ change._id }}/approve" class="btn btn-approve">Approve</a>
                    <a href="/changes/item/{{ change._id }}/deny" class="btn btn-deny">Deny</a>
                </td>
                {% endif %}
            </tr>
        {% endfor %}
    </tbody>
</table>
{% endmacro %}




















