{"$jsonSchema": {"bsonType": "object", "required": ["name", "positive_trait", "negative_trait", "preferred_terrain"], "preview": ["positive_trait", "negative_trait", "founding_nation"], "properties": {"name": {"bsonType": "string", "label": "Name", "description": "The race's name"}, "founding_nation": {"bsonType": "linked_object", "label": "Founding Nation", "description": "The first nation of this race", "collections": ["nations"]}, "positive_trait": {"bsonType": "enum", "label": "Positive Trait", "description": "The positive trait associated with this race", "enum": ["None", "Airborne", "Abstemious", "Aggressive", "Aquatic", "<PERSON><PERSON>", "Charismatic", "Courageous", "Curious", "Docile", "Fecund", "Industrious", "Lucky", "Magical", "Semi-Aquatic", "<PERSON><PERSON><PERSON>", "Swift", "Undying", "Venerable"], "laws": {"Airborne": {"nation_mundane_unit_terrain_speed_cost": -1}, "Abstemious": {"nation_food_consumption": -3, "nation_food_consumption_minimum": 3}, "Aggressive": {"nation_attack": 1}, "Aquatic": {}, "Arcane": {"character_magic_cost": -1, "character_magic_cost_minimum": 2}, "Charismatic": {"nation_trade_slots": 6}, "Courageous": {"nation_civilian_unit_hp": 1, "nation_civilian_unit_damage": 1}, "Curious": {"nation_technology_cost_modifier": -1, "nation_technology_cost_minimum": 2}, "Docile": {"nation_stability_loss_chance": -0.1, "nation_civil_war_chance_maximum": 0.25}, "Fecund": {}, "Industrious": {"nation_district_cost": -0.3, "nation_city_cost": -0.2, "nation_wonder_cost": -0.1}, "Lucky": {"nation_karma": 4}, "Magical": {"nation_spell_cost": -1, "nation_spell_cost_minimum": 2}, "Semi-Aquatic": {"nation_strength_on_water": 1, "nation_naval_unit_speed": 1}, "Sturdy": {"nation_defense": 1, "nation_armor_cannot_be_ignored": 1}, "Swift": {"nation_unit_speed": 1}, "Undying": {"character_elderly_age": 3, "nation_vampiric": 1, "nation_locks_partial_vampire": 1, "nation_vampirism_chance": -100}, "Venerable": {"character_elderly_age": 1, "character_ignore_elderly": 1}}}, "negative_trait": {"bsonType": "enum", "label": "Negative Trait", "description": "The negative trait associated with this race", "enum": ["None", "Acrophobia", "Bloodthirsty", "Clumsy", "<PERSON>", "<PERSON><PERSON>", "Ethereal", "Fleeting", "Fr<PERSON>", "Hydrophobic", "Infertile", "Insatiable", "Irksome", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Rowdy", "Slow", "Unlucky"], "laws": {"Acrophobia": {"nation_mountain_attrition": 0.25, "nation_hill_attrition": 0.25}, "Bloodthirsty": {"nation_stability_loss_chance_per_bloodthirsty_pop": 0.1}, "Clumsy": {"nation_district_cost": 0.2, "nation_city_cost": 0.1, "nation_wonder_cost": 0.05}, "Craven": {"nation_unit_morale": -1, "nation_unit_morale_minimum": 1}, "Dim": {"nation_research_production": -1}, "Ethereal": {"locks_mundane_units": 1, "character_elderly_age": 97, "character_heal_chance": -100, "character_stat_gain_chance_per_cunning": -0.08, "character_title_slots": -1, "nation_food_to_magic_consumption_conversion": 0.5}, "Fleeting": {"character_elderly_age": -1, "character_elderly_death_start_early": 1}, "Frail": {"nation_unit_armor": -1, "nation_unit_armor_minimum": 0}, "Hydrophobic": {"nation_land_strength_on_water": -3, "nation_naval_strength": -2, "nation_locks_fisherman": 1}, "Infertile": {}, "Insatiable": {"nation_food_consumption": 3}, "Irksome": {"nation_trade_slots_mult": -0.5}, "Meek": {}, "Mundane": {"nation_spell_cost": 2}, "Rowdy": {"nation_stability_loss_chance_at_united": 0.35, "nation_stability_loss_chance_at_stable": 0.15}, "Slow": {"nation_unit_speed": -1, "nation_unit_speed_minimum": 2}, "Unlucky": {"nation_karma": -4}}}, "preferred_terrain": {"bsonType": "enum", "label": "Preferred Terrain", "description": "The preferred terrain of this race", "enum": ["None", "Plains", "<PERSON><PERSON>", "Desert", "Forest", "River", "Dense Forest", "Marsh", "Hill", "Mountain", "Hazardous"]}, "pops": {"bsonType": "array", "label": "Pops", "description": "Pops that have this race", "collections": ["pops"], "queryTargetAttribute": "race", "preview": ["nation", "culture", "religion"], "items": {"bsonType": "linked_object"}}}}}